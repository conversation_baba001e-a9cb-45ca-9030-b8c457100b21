// Price Tracker component for KPI Dashboard
import { NotificationSystem } from "../../core/notifications.js";
import { connectionManager } from "../../core/connection.js";

// Load ApexCharts library
if (!window.ApexCharts) {
  const apexChartsScript = document.createElement('script');
  apexChartsScript.src = './analytics/apexcharts.min.js';
  apexChartsScript.async = false; // Load synchronously to ensure it's available
  apexChartsScript.onload = () => {
    console.log('ApexCharts loaded successfully from:', apexChartsScript.src);
  };
  apexChartsScript.onerror = () => {
    console.error('Failed to load ApexCharts from:', apexChartsScript.src);
  };
  document.head.appendChild(apexChartsScript);
}

export class PriceTrackerComponent {
  constructor(container) {
    this.container = container;
    this.partHistoryData = [];
    this.filteredData = [];
    this.pivotData = [];
    this.currentPage = 1;
    this.itemsPerPage = 10; // Default pagination to match opportunity.js pattern
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'inventoryId';
    this.sortDirection = 'asc';
    this.filterStatus = 'all'; // all, stock, nonstock
    this.isLoading = true;
    this.dateRange = {
      start: null,
      end: null,
      preset: '1Y' // 3M, 6M, 1Y, 2Y, Custom
    };
    
    // Database related properties
    this.db = null;
    this.dbName = 'priceTrackerDB';
    this.dbVersion = 1;
    this.storeName = 'partHistory';
    this.settingsStoreName = 'settings';
    this.dbReady = false;
    
    // Notification system
    this.notificationSystem = new NotificationSystem();
    
    // API endpoint
    this.apiEndpoint = 'https://envent-eng.acumatica.com/odata/Envent%20CA%20-%20Live/PartPurchaseHistoryGI';
    
    // Month columns for pivot table
    this.monthColumns = [];

    // Currency conversion settings - only converts USD to CAD
    this.currencyConversion = {
      enabled: true,
      fromCurrency: 'USD', // Only USD will be converted
      toCurrency: 'CAD',   // Only convert to CAD
      rate: 1.38 // Default rate USD to CAD
    };

    // Display settings
    this.displaySettings = {
      showCurrency: true, // Option to show/hide currency in unit cost field
      showUsdAmounts: true, // Option to show/hide USD amounts when converted
      showPurchaseCounts: true // Option to show/hide purchase counts
    };

    // Chart state
    this.expandedCharts = new Set(); // Track which parts have expanded charts
    this.chartInstances = new Map(); // Store chart instances for cleanup

    // Purchase Order drill-down state
    this.poDbName = 'purchaseOrdersDb'; // Purchase order database name
    this.poStoreName = 'purchaseOrders'; // Purchase order store name
    this.poData = new Map(); // Cache for purchase order data
    this.poDataLoaded = false; // Track if PO data has been loaded
  }

  async init() {
    console.log("Initializing Price Tracker component");

    // Render the initial view with loading state
    this.showLoading();
    this.render();

    try {
      // Initialize IndexedDB - this is critical for offline functionality
      try {
        await this.initDatabase();
        console.log("IndexedDB initialized successfully");
      } catch (dbError) {
        console.error("IndexedDB initialization failed:", dbError);
        this.dbReady = false;
        // Continue without IndexedDB - will show appropriate messages to user
      }

      // Load settings including currency conversion
      try {
        await this.loadSettings();
      } catch (settingsError) {
        console.error("Error loading settings, continuing with defaults:", settingsError);
        // Continue with default settings if there's an error
      }

      // Set default date range
      this.setDefaultDateRange();

      // Load data - this will handle the case where IndexedDB is not available
      await this.loadPriceData();

      // Load purchase order data for drill-down functionality (don't wait for it)
      this.loadPurchaseOrderData().catch(error => {
        console.warn("Could not load purchase order data for drill-down:", error);
      });

      // Update loading state and render again
      this.isLoading = false;
      this.hideLoading();
      this.render();

      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing price tracker:", error);
      this.isLoading = false;
      this.hideLoading();
      this.render();
      this.showError("Failed to initialize Price Tracker: " + error.message);
    }
  }
  
  async initDatabase() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName, this.dbVersion);

        request.onerror = (event) => {
          console.error("IndexedDB error:", event.target.error);
          // IndexedDB is required for offline functionality
          console.error("IndexedDB not available - offline functionality will be limited");
          this.dbReady = false;
          reject(new Error("IndexedDB initialization failed: " + event.target.error));
        };

        request.onupgradeneeded = (event) => {
          console.log("Creating or upgrading price tracker database");
          const db = event.target.result;

          try {
            // Create the partHistory object store if it doesn't exist
            if (!db.objectStoreNames.contains(this.storeName)) {
              const store = db.createObjectStore(this.storeName, { keyPath: "id" });

              // Create indexes for searching and sorting
              store.createIndex("inventoryId", "inventoryId", { unique: false });
              store.createIndex("vendorName", "vendorName", { unique: false });
              store.createIndex("purchaseDate", "purchaseDate", { unique: false });
              store.createIndex("isStock", "isStock", { unique: false });

              console.log("Part history object store created");
            }

            // Create settings store
            if (!db.objectStoreNames.contains(this.settingsStoreName)) {
              db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
              console.log("Settings object store created");
            }
          } catch (upgradeError) {
            console.error("Error during database upgrade:", upgradeError);
            // Continue anyway
          }
        };

        request.onsuccess = (event) => {
          this.db = event.target.result;
          this.dbReady = true;
          console.log("Price tracker database initialized successfully");
          resolve();
        };
      } catch (error) {
        console.error("Error initializing IndexedDB:", error);
        // IndexedDB is required for offline functionality
        console.error("IndexedDB not available - offline functionality will be limited");
        this.dbReady = false;
        reject(new Error("IndexedDB initialization failed: " + error.message));
      }
    });
  }

  setDefaultDateRange() {
    // Set default to show all data (no date filter initially)
    this.dateRange = {
      start: null,
      end: null
    };

    this.generateMonthColumns();
  }

  generateMonthColumns() {
    this.monthColumns = [];
    this.yearGroups = {};

    // If no date range is set, generate columns based on available data
    if (!this.dateRange.start || !this.dateRange.end) {
      if (this.partHistoryData && this.partHistoryData.length > 0) {
        // Find the date range from the actual data
        const dates = this.partHistoryData
          .map(item => item.purchaseDate)
          .filter(date => date instanceof Date && !isNaN(date.getTime()))
          .sort((a, b) => a - b);

        if (dates.length > 0) {
          const start = new Date(dates[0]);
          const end = new Date(dates[dates.length - 1]);
          this.generateMonthColumnsFromRange(start, end);
        } else {
          // No valid dates found, generate default range (last 12 months)
          const now = new Date();
          const oneYearAgo = new Date(now);
          oneYearAgo.setFullYear(now.getFullYear() - 1);
          this.generateMonthColumnsFromRange(oneYearAgo, now);
        }
      } else {
        // No data available, generate minimal range (current month only)
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        this.generateMonthColumnsFromRange(startOfMonth, endOfMonth);
      }
      return;
    }

    // Use the specified date range
    const start = new Date(this.dateRange.start);
    const end = new Date(this.dateRange.end);
    this.generateMonthColumnsFromRange(start, end);
  }

  generateMonthColumnsFromRange(start, end) {
    // Start from the beginning of the start month
    start.setDate(1);
    start.setHours(0, 0, 0, 0);

    const current = new Date(start);

    while (current <= end) {
      const year = current.getFullYear();
      const month = current.getMonth() + 1;
      const monthKey = `${year}-${String(month).padStart(2, '0')}`;
      const monthLabel = current.toLocaleDateString('en-US', { month: 'short' });

      // Group by year for the pivot table structure
      if (!this.yearGroups[year]) {
        this.yearGroups[year] = [];
      }

      this.yearGroups[year].push({
        key: monthKey,
        label: monthLabel,
        month: month,
        date: new Date(current)
      });

      this.monthColumns.push({
        key: monthKey,
        label: `${monthLabel} ${year}`,
        year: year,
        month: month,
        date: new Date(current)
      });

      // Move to next month
      current.setMonth(current.getMonth() + 1);
    }
  }

  async loadPriceData(forceRefresh = false) {
    try {
      // Ensure database is ready before proceeding
      if (!this.dbReady) {
        console.warn("Database not ready, attempting to initialize...");
        await this.initDatabase();
        if (!this.dbReady) {
          throw new Error("IndexedDB is required for offline functionality but could not be initialized");
        }
      }

      this.isLoading = true;
      this.render();

      // Check connection status using the same pattern as opportunity.js
      const connectionStatus = connectionManager.getConnectionStatus();

      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest price data from Acumatica");
        try {
          const result = await this.fetchAcumaticaPriceData();

          if (result.success) {
            // Parse the data and store in IndexedDB
            const data = this.parseAcumaticaData(result.data);
            await this.storeDataInIndexedDB(data);
            console.log(`Refreshed and stored ${data.length} price records in IndexedDB`);

            this.partHistoryData = data;
            // Generate month columns based on the loaded data
            this.generateMonthColumns();
            this.processDataForPivot();
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return data;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to normal loading behavior as fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing price data from Acumatica:", fetchError);
          // Fall through to normal loading behavior as fallback
        }
      }

      // If not forcing refresh or Acumatica refresh failed, follow normal loading flow
      // If not connected to Acumatica, or if offline and we have data in IndexedDB
      if (!connectionStatus.acumatica.isConnected || (!forceRefresh && !navigator.onLine)) {
        // Try to get data from IndexedDB
        const data = await this.getDataFromIndexedDB();

        // If no data in IndexedDB and not connected, show appropriate message
        if (data.length === 0) {
          console.log("No price data in IndexedDB and not connected to Acumatica");
          this.partHistoryData = [];
          this.isLoading = false;
          this.render();
          this.showError("No cached price data available. Please connect to Acumatica to fetch data.");
          return [];
        } else {
          console.log(`Loaded ${data.length} price records from IndexedDB cache`);
          this.partHistoryData = data;
        }
      } else {
        // Connected to Acumatica, fetch real data
        console.log("Fetching price data from Acumatica");
        try {
          const result = await this.fetchAcumaticaPriceData();

          if (result.success) {
            // Parse the data and store in IndexedDB
            const data = this.parseAcumaticaData(result.data);
            await this.storeDataInIndexedDB(data);
            console.log(`Stored ${data.length} price records in IndexedDB`);
            this.partHistoryData = data;
          } else {
            // If error fetching from Acumatica, try IndexedDB
            console.warn("Error fetching from Acumatica, trying IndexedDB:", result.error);
            const data = await this.getDataFromIndexedDB();

            // If still no data, show error
            if (data.length === 0) {
              this.partHistoryData = [];
              this.isLoading = false;
              this.render();
              this.showError("Could not fetch live data: " + result.error + ". No cached data available.");
              return [];
            } else {
              console.log(`Using ${data.length} cached price records from IndexedDB`);
              this.partHistoryData = data;
              this.showError("Could not fetch live data: " + result.error + ". Using cached data.");
            }
          }
        } catch (fetchError) {
          console.error("Error fetching price data from Acumatica:", fetchError);
          // Try to get data from IndexedDB as fallback
          const data = await this.getDataFromIndexedDB();

          // If no data in IndexedDB, show error
          if (data.length === 0) {
            this.partHistoryData = [];
            this.isLoading = false;
            this.render();
            this.showError("Error loading price data: " + fetchError.message + ". No cached data available.");
            return [];
          } else {
            console.log(`Using ${data.length} cached price records from IndexedDB`);
            this.partHistoryData = data;
            this.showError("Error loading price data: " + fetchError.message + ". Using cached data.");
          }
        }
      }

      // Generate month columns based on the loaded data
      this.generateMonthColumns();
      this.processDataForPivot();
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();

      return this.partHistoryData;
    } catch (error) {
      console.error("Error loading price data:", error);
      this.partHistoryData = [];
      // Generate month columns based on the loaded data
      this.generateMonthColumns();
      this.processDataForPivot();
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("Error loading price data: " + error.message + ". Please check your connection and try again.");
      return [];
    }
  }

  async fetchAcumaticaPriceData() {
    try {
      // Check connection status using the same pattern as opportunity.js
      const connectionStatus = connectionManager.getConnectionStatus();

      if (!connectionStatus.acumatica.isConnected) {
        return { success: false, error: "Not connected to Acumatica. Please connect first." };
      }

      const instance = connectionStatus.acumatica.instance;
      if (!instance) {
        return { success: false, error: "Missing Acumatica instance configuration." };
      }

      console.log("Fetching ALL price data from Acumatica OData API...");

      // Update loading message to show progress
      this.updateLoadingMessage("Connecting to Acumatica...");

      // Fetch ALL data without any filters - just the pure endpoint
      const url = this.apiEndpoint;
      console.log(`Fetching from URL:`, url);
      this.updateLoadingMessage("Fetching all price data...");

      // Make request with cookies through the connection manager (same pattern as opportunity.js)
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'  // Include cookies for authentication
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Mark as disconnected and return error
          connectionManager.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': connectionManager.connections });
          }
          return { success: false, error: "Authentication failed. Please reconnect to Acumatica." };
        }
        if (response.status === 500) {
          return { success: false, error: "Server error (500). The OData endpoint may have issues. Please try again later." };
        }
        return { success: false, error: `API error: ${response.status} ${response.statusText}` };
      }

      const data = await response.json();
      const allData = data.value || [];

      const progressMessage = `Fetched ${allData.length} total price records`;
      console.log(progressMessage);
      this.updateLoadingMessage(progressMessage);

      console.log(`Completed fetching ${allData.length} total price records`);
      return { success: true, data: allData };
    } catch (error) {
      console.error("Error fetching Acumatica price data:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaData(apiData) {
    try {
      if (!Array.isArray(apiData)) {
        console.error("Price data is not an array:", apiData);
        return [];
      }

      return apiData.map((item, index) => {
        // Parse date
        const purchaseDate = item.Date ? new Date(item.Date) : null;

        // Clean up inventory ID (trim whitespace)
        const inventoryId = (item.InventoryID || '').trim();

        // Parse unit cost
        const unitCost = parseFloat(item.UnitCost || 0);

        return {
          id: `${item.OrderNbr || 'unknown'}-${item.LineNbr || index}`,
          orderNbr: item.OrderNbr || '',
          inventoryId: inventoryId,
          vendorId: item.Vendor || '',
          vendorName: item.Vendor_2 || 'Unknown Vendor',
          purchaseDate: purchaseDate,
          unitCost: unitCost,
          currency: item.Currency || 'USD', // Add currency field
          isStock: item.Isstock === true,
          receiptNbr: item.ReceiptNbr || '',
          lineNbr: item.LineNbr || 0,
          type: item.Type || '',
          lastModified: new Date()
        };
      }).filter(item => item.inventoryId && item.purchaseDate); // Filter out invalid entries
    } catch (error) {
      console.error("Error parsing Acumatica price data:", error);
      return [];
    }
  }

  async getDataFromIndexedDB() {
    return new Promise((resolve) => {
      if (!this.db || !this.dbReady) {
        console.log("Database not ready, returning empty array");
        resolve([]);
        return;
      }

      try {
        const transaction = this.db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onerror = (event) => {
          console.error("Error getting price data from IndexedDB:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const data = event.target.result || [];
          // Parse stored date strings back to Date objects
          data.forEach(item => {
            try {
              if (item.purchaseDate && typeof item.purchaseDate === 'string') {
                item.purchaseDate = new Date(item.purchaseDate);
              }
              if (item.lastModified && typeof item.lastModified === 'string') {
                item.lastModified = new Date(item.lastModified);
              }
            } catch (dateError) {
              console.warn("Error parsing date for item:", item, dateError);
            }
          });
          console.log(`Retrieved ${data.length} price records from IndexedDB`);
          resolve(data);
        };
      } catch (error) {
        console.error("Error in getDataFromIndexedDB:", error);
        resolve([]); // Return empty array instead of rejecting
      }
    });
  }

  async storeDataInIndexedDB(data) {
    return new Promise((resolve, reject) => {
      if (!this.db || !this.dbReady) {
        console.error("Database not ready, cannot store data");
        reject(new Error("Database not ready"));
        return;
      }

      if (!data || data.length === 0) {
        console.log("No data to store");
        resolve();
        return;
      }

      console.log(`Storing ${data.length} price records in IndexedDB...`);

      try {
        const transaction = this.db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);

        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject(new Error("Transaction failed: " + event.target.error));
        };

        transaction.onabort = (event) => {
          console.error("Transaction aborted:", event.target.error);
          reject(new Error("Transaction aborted: " + event.target.error));
        };

        transaction.oncomplete = () => {
          console.log(`Successfully stored ${data.length} price records in IndexedDB`);
          resolve();
        };

        // Clear existing data first
        const clearRequest = store.clear();

        clearRequest.onsuccess = () => {
          console.log("Cleared existing price data");

          // Process data in batches to avoid overwhelming the browser
          const batchSize = 100;
          let currentIndex = 0;

          const processBatch = () => {
            const batch = data.slice(currentIndex, currentIndex + batchSize);

            batch.forEach(item => {
              try {
                // Convert dates to strings for storage
                const itemToStore = {
                  ...item,
                  purchaseDate: item.purchaseDate ? item.purchaseDate.toISOString() : null,
                  lastModified: item.lastModified ? item.lastModified.toISOString() : null
                };

                const request = store.add(itemToStore);
                request.onerror = (event) => {
                  console.warn("Error storing individual item:", event.target.error, item);
                };
              } catch (itemError) {
                console.warn("Error preparing item for storage:", itemError, item);
              }
            });

            currentIndex += batchSize;

            // Process next batch if there are more items
            if (currentIndex < data.length) {
              // Use setTimeout to prevent blocking the UI
              setTimeout(processBatch, 0);
            }
          };

          // Start processing batches
          processBatch();
        };

        clearRequest.onerror = (event) => {
          console.error("Error clearing price store:", event.target.error);
          reject(new Error("Failed to clear existing data: " + event.target.error));
        };
      } catch (error) {
        console.error("Error in storeDataInIndexedDB:", error);
        reject(new Error("Storage operation failed: " + error.message));
      }
    });
  }

  processDataForPivot() {
    // First filter by date range if specified
    let filteredData = [...this.partHistoryData];

    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      console.log(`Filtering data by date range: ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`);

      const originalCount = filteredData.length;
      filteredData = filteredData.filter(item => {
        if (!item.purchaseDate) return false;
        const itemDate = new Date(item.purchaseDate);
        const isInRange = itemDate >= startDate && itemDate <= endDate;
        return isInRange;
      });

      console.log(`Date filtering: ${originalCount} records -> ${filteredData.length} records`);
    } else {
      console.log("No date range filter applied, showing all data");
    }

    // Group data by InventoryID + Vendor combination to avoid duplicates
    const groupedData = {};

    filteredData.forEach(item => {
      // Create unique key for each part-vendor combination
      const key = `${item.inventoryId}|${item.vendorName}`;

      if (!groupedData[key]) {
        groupedData[key] = {
          inventoryId: item.inventoryId,
          vendorName: item.vendorName,
          isStock: item.isStock,
          monthlyPrices: {}
        };
      }

      // Get month key for this purchase
      const monthKey = `${item.purchaseDate.getFullYear()}-${String(item.purchaseDate.getMonth() + 1).padStart(2, '0')}`;

      // Store all purchases for this month (we might have multiple purchases in same month)
      if (!groupedData[key].monthlyPrices[monthKey]) {
        groupedData[key].monthlyPrices[monthKey] = [];
      }

      groupedData[key].monthlyPrices[monthKey].push({
        unitCost: item.unitCost,
        purchaseDate: item.purchaseDate,
        orderNbr: item.orderNbr,
        currency: item.currency || 'USD' // Include currency information
      });
    });

    // Convert to array and apply additional filters
    this.pivotData = Object.values(groupedData);
    this.applyFilters();
  }

  applyFilters() {
    let filtered = [...this.pivotData];

    // Apply search filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.inventoryId.toLowerCase().includes(searchLower) ||
        item.vendorName.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (this.filterStatus !== 'all') {
      const isStockFilter = this.filterStatus === 'stock';
      filtered = filtered.filter(item => item.isStock === isStockFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'inventoryId':
          aValue = a.inventoryId;
          bValue = b.inventoryId;
          break;
        case 'vendorName':
          aValue = a.vendorName;
          bValue = b.vendorName;
          break;
        case 'isStock':
          aValue = a.isStock ? 'Stock' : 'Non-Stock';
          bValue = b.isStock ? 'Stock' : 'Non-Stock';
          break;
        default:
          aValue = a.inventoryId;
          bValue = b.inventoryId;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (this.sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    this.filteredData = filtered;
    this.calculateTotalPages();
  }

  calculateTotalPages() {
    this.totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = Math.max(1, this.totalPages);
    }
  }



  formatCurrency(amount) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '–';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  formatAmount(amount, currencyCode) {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '–';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
      currencyDisplay: 'symbol',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  formatDate(date) {
    if (!date) return '–';
    if (!(date instanceof Date)) return '–';

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    // Only use the built-in loading UI in the container (like opportunity.js)
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p id="loading-message" class="mt-4 text-gray-600 dark:text-gray-400">Loading price data...</p>
      </div>
    `;
  }

  renderContent() {
    // Clear container
    this.container.innerHTML = '';

    // Create content area
    const contentElement = document.createElement('div');
    contentElement.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4';
    this.container.appendChild(contentElement);

    // Render the header
    contentElement.innerHTML = this.renderHeader();

    // Add the main content
    const mainContent = document.createElement('div');
    mainContent.id = 'price-tracker-main-content';
    if (this.filteredData.length === 0 && !this.isLoading) {
      mainContent.innerHTML = this.renderEmptyState();
    } else {
      mainContent.innerHTML = `
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          ${this.renderPivotTable()}
          ${this.renderPagination()}
        </div>
      `;
    }
    contentElement.appendChild(mainContent);

    // Set up event listeners
    this.setupEventListeners();
  }

  renderTableOnly() {
    // Only update the main content area, preserving the search input and header
    const mainContent = this.container.querySelector('#price-tracker-main-content');
    if (mainContent) {
      if (this.filteredData.length === 0 && !this.isLoading) {
        mainContent.innerHTML = this.renderEmptyState();
      } else {
        mainContent.innerHTML = `
          <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            ${this.renderPivotTable()}
            ${this.renderPagination()}
          </div>
        `;
      }

      // Re-setup event listeners for the new table content only
      this.setupTableEventListeners();
    }
  }

  setupTableEventListeners() {
    // Only setup event listeners that are needed for the table content
    // This avoids re-setting up the search input listener

    // Pagination buttons
    const firstPageBtn = this.container.querySelector('#first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.renderTableOnly();
      });
    }

    const prevPageBtn = this.container.querySelector('#prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.renderTableOnly();
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.renderTableOnly();
        }
      });
    }

    const lastPageBtn = this.container.querySelector('#last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.renderTableOnly();
      });
    }

    // Refresh data button (in empty state)
    const refreshDataBtn = this.container.querySelector('#refresh-data');
    if (refreshDataBtn) {
      refreshDataBtn.addEventListener('click', async () => {
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.isConnected) {
          // If not connected, show connection modal or redirect to connection page
          this.showError("Please connect to Acumatica first to fetch price data.");
          return;
        }
        await this.loadPriceData(true);
        this.renderTableOnly();
      });
    }

    // Clear search button (in empty state)
    const clearSearchEmptyBtn = this.container.querySelector('#clear-search-empty');
    if (clearSearchEmptyBtn) {
      clearSearchEmptyBtn.addEventListener('click', () => {
        this.searchTerm = '';
        const searchInput = this.container.querySelector('#price-search');
        if (searchInput) {
          searchInput.value = '';
        }
        this.applyFilters();
        this.renderTableOnly();
      });
    }
  }

  renderHeader() {
    // Create and append the header with controls
    const headerElement = document.createElement('div');
    // Add a unique class for scoping styles
    headerElement.className = 'price-tracker-component-header flex flex-col md:flex-row justify-between items-center mb-6';

    // Add CSS for expandable tab labels and table styling, scoped to the price tracker component
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* Scope styles to the price tracker component header */
      .price-tracker-component-header .tab-btn {
        position: relative;
        overflow: hidden;
        transition: width 0.3s ease;
        width: 40px; /* Keep initial width small */
        min-width: 40px;
      }
      .price-tracker-component-header .tab-btn.active {
        width: auto; /* Expand active tab */
      }
      .price-tracker-component-header .tab-btn:hover {
        width: auto; /* Expand on hover */
      }
      .price-tracker-component-header .tab-label {
        opacity: 0;
        max-width: 0;
        overflow: hidden;
        white-space: nowrap;
        transition: all 0.3s ease;
        margin-left: 0;
      }
      .price-tracker-component-header .tab-btn.active .tab-label,
      .price-tracker-component-header .tab-btn:hover .tab-label {
        opacity: 1;
        max-width: 100px; /* Adjust max-width as needed */
        margin-left: 6px;
      }

      /* Price tracker table styling */
      .price-tracker-table {
        border-collapse: separate;
        border-spacing: 0;
      }

      .price-tracker-table th,
      .price-tracker-table td {
        border-right: 1px solid #e5e7eb;
        border-bottom: 1px solid #e5e7eb;
      }

      .price-tracker-table th:last-child,
      .price-tracker-table td:last-child {
        border-right: none;
      }

      .price-tracker-table tr:last-child td {
        border-bottom: none;
      }

      /* Ensure sticky columns have proper background */
      .price-tracker-table .sticky-col {
        background-color: white;
        position: sticky;
        z-index: 10;
      }

      .dark .price-tracker-table .sticky-col {
        background-color: #111827;
      }

      /* Year toggle and chart toggle button styles */
      .year-toggle-btn, .chart-toggle-btn {
        transition: all 0.2s ease;
      }

      .year-toggle-btn:hover, .chart-toggle-btn:hover {
        transform: scale(1.1);
      }

      .chart-row {
        animation: slideDown 0.2s ease-out;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          max-height: 0;
        }
        to {
          opacity: 1;
          max-height: 400px;
        }
      }

      /* Chart container styling */
      .chart-row .apexcharts-canvas {
        background: transparent !important;
      }

      /* Price cell clickable styling */
      .price-cell-clickable {
        position: relative;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .price-cell-clickable:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .dark .price-cell-clickable:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .price-cell-clickable .fa-external-link-alt {
        font-size: 10px;
      }

      /* Tooltip styling for price cells */
      .price-cell-clickable::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 1000;
      }

      .price-cell-clickable:hover::after {
        opacity: 1;
      }

      /* Modal styling improvements */
      #po-details-modal .modal-content {
        max-height: 80vh;
        overflow-y: auto;
      }

      /* Purchase detail card styling */
      .purchase-detail-card {
        transition: all 0.2s ease;
      }

      .purchase-detail-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .dark .purchase-detail-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
      }

      /* Highlighted line item styling */
      .line-item-highlighted {
        background-color: rgba(59, 130, 246, 0.1);
        border-left: 4px solid #3b82f6;
      }

      .dark .line-item-highlighted {
        background-color: rgba(59, 130, 246, 0.2);
      }

      /* Clicked item indicator animation */
      .clicked-item-indicator {
        animation: pulse-blue 2s infinite;
      }

      @keyframes pulse-blue {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      /* Line item table improvements */
      .line-items-table {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e5e7eb;
      }

      .dark .line-items-table {
        border-color: #374151;
      }

      .line-items-table th {
        background: #f9fafb;
        font-weight: 600;
      }

      .dark .line-items-table th {
        background: #374151;
      }

      .line-items-table tbody tr:hover {
        background: #f3f4f6;
      }

      .dark .line-items-table tbody tr:hover {
        background: #4b5563;
      }

      .line-items-table .line-item-highlighted:hover {
        background: linear-gradient(90deg, rgba(59, 130, 246, 0.15) 0%, rgba(59, 130, 246, 0.08) 100%);
      }

      .dark .line-items-table .line-item-highlighted:hover {
        background: linear-gradient(90deg, rgba(59, 130, 246, 0.25) 0%, rgba(59, 130, 246, 0.15) 100%);
      }
    `;
    document.head.appendChild(styleEl);

    headerElement.innerHTML = `
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Price Tracker</h2>

      <div class="flex flex-wrap items-center gap-2">
        <div class="relative">
          <input
            type="text"
            id="price-search"
            placeholder="Search parts..."
            class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 text-gray-900 dark:text-white"
            value="${this.searchTerm || ''}"
          >
          <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 text-gray-900 dark:text-white">
          <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Parts</option>
          <option value="stock" ${this.filterStatus === 'stock' ? 'selected' : ''}>Stock Items</option>
          <option value="nonstock" ${this.filterStatus === 'nonstock' ? 'selected' : ''}>Non-Stock Items</option>
        </select>

        <div class="flex gap-2">
          <!-- Date Range Button -->
          <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Date Range">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              <circle cx="12" cy="14" r="0.5" stroke="currentColor" stroke-width="2" />
            </svg>
          </button>

          <!-- Refresh Button -->
          <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Refresh Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>

          <!-- Export Button -->
          <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Export Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
            </svg>
          </button>

          <!-- Settings Button -->
          <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors" title="Settings">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>
    `;

    return headerElement.outerHTML;
  }





  updateLoadingMessage(message) {
    const loadingElement = this.container?.querySelector('#loading-message');
    if (loadingElement) {
      loadingElement.textContent = message;
    }
  }

  renderEmptyState() {
    const connectionStatus = connectionManager.getConnectionStatus();
    const isConnected = connectionStatus.acumatica.isConnected;

    let message, actionText, actionClass;

    if (this.searchTerm) {
      message = 'No parts match your search criteria. Try adjusting your search terms or clearing the search.';
      actionText = 'Clear Search';
      actionClass = 'bg-gray-600 hover:bg-gray-700';
    } else if (!isConnected) {
      message = 'No cached price data available. Please connect to Acumatica to fetch the latest price data.';
      actionText = 'Connect to Acumatica';
      actionClass = 'bg-blue-600 hover:bg-blue-700';
    } else {
      message = 'No price data available for the selected criteria. Try adjusting the date range or refreshing the data.';
      actionText = 'Refresh Data';
      actionClass = 'bg-blue-600 hover:bg-blue-700';
    }

    return `
      <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No price data found</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4 max-w-md mx-auto">
            ${message}
          </p>
          <div class="flex flex-col sm:flex-row gap-2 justify-center">
            <button id="refresh-data" class="${actionClass} text-white px-4 py-2 rounded-md transition-colors">
              ${actionText}
            </button>
            ${this.searchTerm ? `
              <button id="clear-search-empty" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors">
                Clear Search
              </button>
            ` : ''}
          </div>
        </div>
      </div>
    `;
  }

  renderPivotTable() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredData.slice(startIndex, endIndex);

    // Get unique years for the header structure
    const years = Object.keys(this.yearGroups).sort();

    return `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed price-tracker-table">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <!-- Year header row -->
            <tr>
              <!-- Fixed columns with proper widths -->
              <th rowspan="2" class="sticky left-0 z-20 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r-2 border-gray-300 dark:border-gray-600" data-sort="inventoryId" style="width: 180px; min-width: 180px;">
                Inventory ID <span class="sort-indicator">${this.sortField === 'inventoryId' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>
              <th rowspan="2" class="sticky left-0 z-20 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r-2 border-gray-300 dark:border-gray-600" data-sort="vendorName" style="left: 180px; width: 200px; min-width: 200px;">
                Vendor <span class="sort-indicator">${this.sortField === 'vendorName' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>
              <th rowspan="2" class="sticky left-0 z-20 px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer bg-gray-50 dark:bg-gray-800 border-r-2 border-gray-300 dark:border-gray-600" data-sort="isStock" style="left: 380px; width: 100px; min-width: 100px;">
                Type <span class="sort-indicator">${this.sortField === 'isStock' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
              </th>

              <!-- Year columns -->
              ${years.map(year => `
                <th colspan="${this.yearGroups[year].length}" class="px-3 py-2 text-center text-sm font-bold text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border-l-2 border-gray-400 dark:border-gray-500">
                  ${year}
                </th>
              `).join('')}
            </tr>

            <!-- Month header row -->
            <tr>
              <!-- Month columns under each year -->
              ${years.map(year =>
                this.yearGroups[year].map(month => `
                  <th class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600" style="width: 90px; min-width: 90px;">
                    ${month.label}
                  </th>
                `).join('')
              ).join('')}
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${pageData.map(row => this.renderTableRow(row)).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  renderTableRow(row) {
    const years = Object.keys(this.yearGroups).sort();
    const partKey = `${row.inventoryId}|${row.vendorName}`;
    const hasChart = this.expandedCharts.has(partKey);

    return `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <!-- Fixed columns with proper widths and positioning -->
        <td class="sticky left-0 z-10 px-3 py-4 text-sm text-blue-600 dark:text-blue-400 font-medium bg-white dark:bg-gray-900 border-r-2 border-gray-300 dark:border-gray-600" style="width: 180px; min-width: 180px; max-width: 180px; overflow: hidden; text-overflow: ellipsis;">
          <div class="flex items-center gap-2">
            <button type="button" class="chart-toggle-btn text-gray-400 hover:text-blue-600 transition-colors cursor-pointer" data-part-key="${partKey}" title="Toggle Price Chart">
              <svg class="w-4 h-4 transform transition-transform ${hasChart ? 'rotate-90' : ''}" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </button>
            <div class="truncate" title="${this.escapeHtml(row.inventoryId)}">
              ${this.escapeHtml(row.inventoryId)}
            </div>
          </div>
        </td>
        <td class="sticky left-0 z-10 px-3 py-4 text-sm text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-900 border-r-2 border-gray-300 dark:border-gray-600" style="left: 180px; width: 200px; min-width: 200px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
          <div class="truncate" title="${this.escapeHtml(row.vendorName)}">
            ${this.escapeHtml(row.vendorName)}
          </div>
        </td>
        <td class="sticky left-0 z-10 px-3 py-4 text-sm bg-white dark:bg-gray-900 border-r-2 border-gray-300 dark:border-gray-600" style="left: 380px; width: 100px; min-width: 100px; max-width: 100px;">
          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${row.isStock ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}">
            ${row.isStock ? 'Stock' : 'Non-Stock'}
          </span>
        </td>

        <!-- Dynamic month columns organized by year -->
        ${years.map(year =>
          this.yearGroups[year].map(month => {
            const monthData = row.monthlyPrices[month.key];
            return `
              <td class="px-2 py-4 whitespace-nowrap text-sm text-center text-gray-800 dark:text-gray-200 border-l border-gray-200 dark:border-gray-600" style="width: 90px; min-width: 90px; max-width: 90px;">
                ${this.renderMonthCell(monthData, row.inventoryId, row.vendorName, month.key)}
              </td>
            `;
          }).join('')
        ).join('')}
      </tr>
    `;
  }





  renderMonthCell(monthData, inventoryId, vendorName, monthKey) {
    if (!monthData || monthData.length === 0) {
      return '<span class="text-gray-400">–</span>';
    }

    // Get the latest purchase for this month
    const latest = monthData.length === 1 ? monthData[0] :
      monthData.reduce((latest, current) =>
        current.purchaseDate > latest.purchaseDate ? current : latest
      );

    const originalPrice = parseFloat(latest.unitCost);
    const originalCurrency = latest.currency || 'USD';

    // Calculate converted price - only convert if source currency is USD and target is CAD
    let displayPrice = originalPrice;
    let displayCurrency = originalCurrency;

    if (this.currencyConversion.enabled &&
        originalCurrency === 'USD' &&
        this.currencyConversion.toCurrency === 'CAD') {
      displayPrice = originalPrice * this.currencyConversion.rate;
      displayCurrency = this.currencyConversion.toCurrency;
    }

    // Format the price with currency
    const formattedPrice = this.displaySettings.showCurrency ?
      this.formatAmount(displayPrice, displayCurrency) :
      `$${displayPrice.toFixed(2)}`;

    // Check if we should show original USD amounts - only when USD was converted to CAD
    const shouldShowOriginal = this.currencyConversion.enabled &&
                              originalCurrency === 'USD' &&
                              displayCurrency === 'CAD' &&
                              this.displaySettings.showUsdAmounts;

    // Create unique identifier for this cell
    const cellId = `price-cell-${inventoryId}-${vendorName}-${monthKey}`.replace(/[^a-zA-Z0-9-]/g, '-');

    if (monthData.length === 1) {
      // Single purchase in this month
      return `
        <div class="text-center price-cell-clickable cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 rounded p-1 transition-colors"
             data-cell-id="${cellId}"
             data-inventory-id="${this.escapeHtml(inventoryId)}"
             data-vendor-name="${this.escapeHtml(vendorName)}"
             data-month-key="${monthKey}"
             title="Click to view purchase order details">
          <span class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">${formattedPrice}</span>
          ${shouldShowOriginal ?
            `<div class="text-xs text-gray-400 dark:text-gray-500">
              (${this.formatAmount(originalPrice, originalCurrency)})
            </div>` : ''}
          <div class="text-xs text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
            <i class="fas fa-external-link-alt"></i>
          </div>
        </div>
      `;
    } else {
      // Multiple purchases in this month - show latest price with count
      const purchaseCountText = this.displaySettings.showPurchaseCounts ?
        `<span class="text-xs text-gray-500 dark:text-gray-400">(${monthData.length} purchases)</span>` : '';

      return `
        <div class="text-center price-cell-clickable cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 rounded p-1 transition-colors group"
             data-cell-id="${cellId}"
             data-inventory-id="${this.escapeHtml(inventoryId)}"
             data-vendor-name="${this.escapeHtml(vendorName)}"
             data-month-key="${monthKey}"
             title="Click to view ${monthData.length} purchase order details">
          <span class="font-medium block text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">${formattedPrice}</span>
          ${shouldShowOriginal ?
            `<div class="text-xs text-gray-400 dark:text-gray-500">
              (${this.formatAmount(originalPrice, originalCurrency)})
            </div>` : ''}
          ${purchaseCountText}
          <div class="text-xs text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
            <i class="fas fa-external-link-alt"></i>
          </div>
        </div>
      `;
    }
  }

  renderPagination() {
    if (this.totalPages <= 1) {
      return '';
    }

    return `
      <!-- Updated Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredData.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredData.length)} of
          ${this.filteredData.length} results
        </div>

        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
  }

  debugButtons() {
    console.log('=== DEBUGGING BUTTONS ===');
    const chartBtns = this.container.querySelectorAll('.chart-toggle-btn');

    console.log('Chart toggle buttons found:', chartBtns.length);
    chartBtns.forEach((btn, i) => {
      console.log(`Chart button ${i}:`, btn.getAttribute('data-part-key'), btn);
    });

    console.log('Event delegation setup on container:', this.container);
    console.log('=== END DEBUG ===');
  }

  setupEventListeners() {
    // Search functionality with debouncing (300ms like opportunity.js)
    let searchTimeout;
    const searchInput = this.container.querySelector('#price-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          this.searchTerm = e.target.value;
          this.currentPage = 1;
          this.applyFilters();
          this.renderTableOnly(); // Only re-render table content, not the entire component
        }, 300); // 300ms debounce to match opportunity.js pattern
      });
    }

    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        const searchInput = this.container.querySelector('#price-search');
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
        this.renderTableOnly();
      });
    }

    // Status filter
    const statusFilter = this.container.querySelector('#status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', (e) => {
        this.filterStatus = e.target.value;
        this.currentPage = 1;
        this.applyFilters();
        this.renderTableOnly();
      });
    }

    // Date range button
    const dateRangeBtn = this.container.querySelector('#date-range-button');
    if (dateRangeBtn) {
      dateRangeBtn.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }

    // Refresh button
    const refreshBtn = this.container.querySelector('#refresh-button');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        await this.loadPriceData(true); // Force refresh
        this.setupEventListeners();
      });
    }

    // Export button
    const exportBtn = this.container.querySelector('#export-button');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportData();
      });
    }

    // Settings button
    const settingsBtn = this.container.querySelector('#settings-button');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettingsModal();
      });
    }

    // Table sorting
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.renderTableOnly();
      });
    });

    // Setup table-specific event listeners
    this.setupTableEventListeners();

    // Debug buttons first
    this.debugButtons();

    // Use event delegation for chart buttons and price cells (more reliable)
    this.container.addEventListener('click', (e) => {
      // Price cell clicks for purchase order drill-down
      if (e.target.closest('.price-cell-clickable')) {
        e.preventDefault();
        e.stopPropagation();
        const cell = e.target.closest('.price-cell-clickable');
        const inventoryId = cell.getAttribute('data-inventory-id');
        const vendorName = cell.getAttribute('data-vendor-name');
        const monthKey = cell.getAttribute('data-month-key');
        console.log('Price cell clicked:', { inventoryId, vendorName, monthKey });
        this.showPurchaseOrderDetails(inventoryId, vendorName, monthKey);
        return;
      }

      // Chart toggle buttons
      if (e.target.closest('.chart-toggle-btn')) {
        e.preventDefault();
        e.stopPropagation();
        const btn = e.target.closest('.chart-toggle-btn');
        const partKey = btn.getAttribute('data-part-key');
        console.log('Chart toggle clicked:', partKey);
        this.toggleChart(partKey);
        return;
      }

      // Chart close buttons
      if (e.target.closest('.chart-close-btn')) {
        e.preventDefault();
        e.stopPropagation();
        const btn = e.target.closest('.chart-close-btn');
        const partKey = btn.getAttribute('data-part-key');
        console.log('Chart close clicked:', partKey);
        this.closeChart(partKey);
        return;
      }
    });
  }



  toggleChart(partKey) {
    console.log('Toggling chart for:', partKey);

    // Check if chart row already exists
    const existingChartRow = this.container.querySelector(`tr.chart-row[data-part-key="${partKey}"]`);

    if (existingChartRow) {
      // Close chart - just remove the row
      console.log('Closing chart for:', partKey);
      this.closeChart(partKey);
    } else {
      // Open chart - add chart row after the current row
      console.log('Opening chart for:', partKey);
      this.addChartRow(partKey);
    }
  }

  closeChart(partKey) {
    // Clean up existing chart instance
    if (this.chartInstances.has(partKey)) {
      const chart = this.chartInstances.get(partKey);
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
      this.chartInstances.delete(partKey);
    }

    // Remove chart row from DOM
    const chartRow = this.container.querySelector(`tr.chart-row[data-part-key="${partKey}"]`);
    if (chartRow) {
      chartRow.remove();
    }

    this.expandedCharts.delete(partKey);
  }

  addChartRow(partKey) {
    // Find the table row for this part
    const [inventoryId, vendorName] = partKey.split('|');
    const tableRows = this.container.querySelectorAll('tbody tr:not(.chart-row)');

    let targetRow = null;
    tableRows.forEach(row => {
      const firstCell = row.querySelector('td');
      if (firstCell && firstCell.textContent.trim() === inventoryId) {
        targetRow = row;
      }
    });

    if (!targetRow) {
      console.warn('Could not find target row for:', partKey);
      return;
    }

    // Create chart row
    const chartRow = document.createElement('tr');
    chartRow.className = 'chart-row bg-gray-50 dark:bg-gray-800';
    chartRow.setAttribute('data-part-key', partKey);

    // Count columns for colspan
    const headerCells = this.container.querySelectorAll('thead th');
    const colCount = headerCells.length;

    chartRow.innerHTML = `
      <td colspan="${colCount}" class="p-4">
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
              Price Trend: ${this.escapeHtml(inventoryId)} - ${this.escapeHtml(vendorName)}
            </h4>
            <button type="button" class="chart-close-btn text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer" data-part-key="${partKey}">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
          <div class="chart-container w-full" style="height: 250px;" data-part-key="${partKey}">
            <div class="flex items-center justify-center h-full text-gray-500">Loading chart...</div>
          </div>
        </div>
      </td>
    `;

    // Insert after target row
    targetRow.parentNode.insertBefore(chartRow, targetRow.nextSibling);

    // Add to expanded charts set
    this.expandedCharts.add(partKey);

    // Create chart after a short delay
    setTimeout(() => {
      this.createSimpleChart(partKey);
    }, 100);
  }

  createSimpleChart(partKey) {
    console.log('Creating simple chart for:', partKey);

    // Wait for ApexCharts to be loaded
    if (typeof ApexCharts === 'undefined') {
      console.log('ApexCharts not loaded yet, retrying...');
      setTimeout(() => this.createSimpleChart(partKey), 200);
      return;
    }

    // Find chart container
    const chartElement = this.container.querySelector(`.chart-container[data-part-key="${partKey}"]`);

    if (!chartElement) {
      console.warn(`Chart container not found for: ${partKey}`);
      return;
    }

    // Find the row data for this part
    const [inventoryId, vendorName] = partKey.split('|');
    const rowData = this.filteredData.find(row =>
      row.inventoryId === inventoryId && row.vendorName === vendorName
    );

    if (!rowData) {
      console.warn(`Row data not found for: ${partKey}`);
      chartElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No data found</div>';
      return;
    }

    // Prepare monthly chart data
    const chartData = this.prepareMonthlyChartData(rowData);

    if (chartData.length === 0) {
      chartElement.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No price data available</div>';
      return;
    }

    // Clear loading message
    chartElement.innerHTML = '';

    // Very simple chart options
    const options = {
      series: [{
        name: 'Unit Cost (CAD)',
        data: chartData
      }],
      chart: {
        type: 'line',
        height: 220,
        toolbar: { show: false },
        animations: { enabled: false }
      },
      stroke: {
        curve: 'straight',
        width: 3
      },
      colors: ['#3B82F6'],
      xaxis: {
        type: 'category',
        labels: {
          style: { fontSize: '12px' }
        }
      },
      yaxis: {
        labels: {
          formatter: function(value) {
            return '$' + value.toFixed(2);
          },
          style: { fontSize: '12px' }
        }
      },
      tooltip: {
        y: {
          formatter: function(value) {
            return '$' + value.toFixed(2);
          }
        }
      },
      markers: {
        size: 5,
        strokeWidth: 2,
        strokeColors: '#fff'
      },
      grid: {
        borderColor: '#e5e7eb'
      }
    };

    // Create and render chart with timeout protection
    try {
      const chart = new ApexCharts(chartElement, options);

      // Set a timeout to prevent hanging
      const renderTimeout = setTimeout(() => {
        console.warn('Chart render timeout for:', partKey);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full text-yellow-500">Chart timeout</div>';
      }, 5000);

      chart.render().then(() => {
        clearTimeout(renderTimeout);
        console.log('Chart rendered successfully for:', partKey);
      }).catch(error => {
        clearTimeout(renderTimeout);
        console.error('Chart render error:', error);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full text-red-500">Render error</div>';
      });

      this.chartInstances.set(partKey, chart);
    } catch (error) {
      console.error('Error creating chart:', error);
      chartElement.innerHTML = '<div class="flex items-center justify-center h-full text-red-500">Chart error</div>';
    }
  }

  prepareMonthlyChartData(rowData) {
    const chartData = [];

    // Get sorted month keys
    const monthKeys = Object.keys(rowData.monthlyPrices).sort();

    monthKeys.forEach(monthKey => {
      const monthData = rowData.monthlyPrices[monthKey];
      if (monthData && monthData.length > 0) {
        // Use the latest price for each month
        const latestPrice = monthData.reduce((latest, current) =>
          current.purchaseDate > latest.purchaseDate ? current : latest
        );

        let displayPrice = latestPrice.unitCost;

        // Apply currency conversion if enabled - only convert USD to CAD
        if (this.currencyConversion.enabled &&
            latestPrice.currency === 'USD' &&
            this.currencyConversion.toCurrency === 'CAD') {
          displayPrice = latestPrice.unitCost * this.currencyConversion.rate;
        }

        if (!isNaN(displayPrice)) {
          // Use month label instead of timestamp for simpler display
          const monthLabel = monthKey; // e.g., "2024-01"
          chartData.push({
            x: monthLabel,
            y: displayPrice
          });
        }
      }
    });

    return chartData;
  }

  showDateRangePicker() {
    // Remove any existing modal first
    const existingModal = document.getElementById('date-range-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create date range picker modal (same as opportunity.js)
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    // Format dates for input fields
    const formatDateForInput = (date) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const startDateValue = this.dateRange.start ? formatDateForInput(this.dateRange.start) : formatDateForInput(oneMonthAgo);
    const endDateValue = this.dateRange.end ? formatDateForInput(this.dateRange.end) : formatDateForInput(today);

    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Select Date Range</h3>

        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Filter price data by purchase date. Only purchases made within this date range will be displayed.
          </p>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input
                type="date"
                id="date-range-start"
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${startDateValue}"
              >
            </div>

            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input
                type="date"
                id="date-range-end"
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${endDateValue}"
              >
            </div>
          </div>
        </div>

        <div class="flex justify-between">
          <div>
            <button id="date-range-clear" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
      </div>
    `;

    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'date-range-modal';

    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full mx-4';
    modalContainer.innerHTML = modalContent;

    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);

    // Setup event listeners for the modal
    const cancelButton = document.getElementById('date-range-cancel');
    const applyButton = document.getElementById('date-range-apply');
    const clearButton = document.getElementById('date-range-clear');
    const startDateInput = document.getElementById('date-range-start');
    const endDateInput = document.getElementById('date-range-end');

    // Keyboard event handler
    let keydownHandler = null;

    const closeModal = () => {
      try {
        // Remove keyboard event listener
        if (keydownHandler) {
          document.removeEventListener('keydown', keydownHandler);
          keydownHandler = null;
        }

        // Remove modal from DOM
        const modal = document.getElementById('date-range-modal');
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      } catch (error) {
        console.warn("Error closing date range modal:", error);
      }
    };

    if (cancelButton) {
      cancelButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      });
    }

    if (clearButton) {
      clearButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        this.dateRange = {
          start: null,
          end: null
        };

        // Reset to default filtering and regenerate month columns
        this.currentPage = 1;
        this.generateMonthColumns();
        this.processDataForPivot();
        this.calculateTotalPages();
        this.render();
        this.setupEventListeners();

        closeModal();
        this.showSuccess("Date filter cleared - showing all data");
      });
    }

    if (applyButton) {
      applyButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const startDateValue = startDateInput.value;
        const endDateValue = endDateInput.value;

        if (!startDateValue || !endDateValue) {
          this.showError("Please select both start and end dates");
          return;
        }

        const startDate = new Date(startDateValue);
        const endDate = new Date(endDateValue);

        // Validate dates
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          this.showError("Invalid date format");
          return;
        }

        if (startDate > endDate) {
          this.showError("Start date cannot be after end date");
          return;
        }

        // Apply date filter
        this.dateRange = {
          start: startDate,
          end: endDate
        };

        // Apply filters and reset to first page
        this.currentPage = 1;
        this.generateMonthColumns();
        this.processDataForPivot();
        this.calculateTotalPages();
        this.render();
        this.setupEventListeners();

        closeModal();

        const startStr = startDate.toLocaleDateString();
        const endStr = endDate.toLocaleDateString();
        const filteredCount = this.filteredData ? this.filteredData.length : 0;
        this.showSuccess(`Date filter applied: ${startStr} to ${endStr} (${filteredCount} parts found)`);
      });
    }

    // Close modal when clicking outside
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      }
    });

    // Close modal with Escape key
    keydownHandler = (e) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        closeModal();
      }
    };
    document.addEventListener('keydown', keydownHandler);
  }



  exportData() {
    try {
      // Prepare data for export
      const exportData = [];

      this.filteredData.forEach(row => {
        const baseRow = {
          'Inventory ID': row.inventoryId,
          'Vendor Name': row.vendorName,
          'Type': row.isStock ? 'Stock' : 'Non-Stock'
        };

        // Add month columns with full currency information
        this.monthColumns.forEach(month => {
          const monthData = row.monthlyPrices[month.key];
          if (monthData && monthData.length > 0) {
            let latest;
            if (monthData.length === 1) {
              latest = monthData[0];
            } else {
              // For multiple purchases, use latest price
              latest = monthData.reduce((latest, current) =>
                current.purchaseDate > latest.purchaseDate ? current : latest
              );
            }

            const originalPrice = parseFloat(latest.unitCost);
            const originalCurrency = latest.currency || 'USD';

            // Calculate converted price - only convert if source currency is USD and target is CAD
            let displayPrice = originalPrice;
            let displayCurrency = originalCurrency;

            if (this.currencyConversion.enabled &&
                originalCurrency === 'USD' &&
                this.currencyConversion.toCurrency === 'CAD') {
              displayPrice = originalPrice * this.currencyConversion.rate;
              displayCurrency = this.currencyConversion.toCurrency;
            }

            // Format the export value with full currency information
            let exportValue = '';

            // Always show the converted/display price with currency symbol
            const formattedDisplayPrice = this.formatAmount(displayPrice, displayCurrency);
            exportValue = formattedDisplayPrice;

            // Add original USD amount in parentheses if conversion occurred
            const shouldShowOriginal = this.currencyConversion.enabled &&
                                      originalCurrency === 'USD' &&
                                      displayCurrency === 'CAD' &&
                                      this.displaySettings.showUsdAmounts;

            if (shouldShowOriginal) {
              const formattedOriginalPrice = this.formatAmount(originalPrice, originalCurrency);
              exportValue += ` (${formattedOriginalPrice})`;
            }

            // Add purchase count if multiple purchases and setting is enabled
            if (monthData.length > 1 && this.displaySettings.showPurchaseCounts) {
              exportValue += ` (${monthData.length} purchases)`;
            }

            baseRow[month.label] = exportValue;
          } else {
            baseRow[month.label] = '';
          }
        });

        exportData.push(baseRow);
      });

      // Convert to CSV
      if (exportData.length === 0) {
        this.showError('No data to export');
        return;
      }

      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in CSV
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `price_tracker_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.showSuccess('Data exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      this.showError('Failed to export data: ' + error.message);
    }
  }

  // Purchase Order drill-down methods
  async loadPurchaseOrderData() {
    if (this.poDataLoaded) {
      return; // Already loaded
    }

    try {
      console.log("Loading purchase order data for drill-down functionality");
      const poRecords = await this.getPurchaseOrdersFromIndexedDB();

      // Create a map for quick lookup by order number and inventory ID
      this.poData.clear();

      poRecords.forEach(po => {
        // Index by order number for quick lookup
        if (po.orderNbr) {
          if (!this.poData.has(po.orderNbr)) {
            this.poData.set(po.orderNbr, []);
          }
          this.poData.get(po.orderNbr).push(po);
        }
      });

      this.poDataLoaded = true;
      console.log(`Loaded ${poRecords.length} purchase orders for drill-down, indexed ${this.poData.size} unique order numbers`);
    } catch (error) {
      console.error("Error loading purchase order data:", error);
      // Don't throw error, just log it - drill-down will show limited info
    }
  }

  async getPurchaseOrdersFromIndexedDB() {
    return new Promise((resolve) => {
      try {
        const request = indexedDB.open(this.poDbName);

        request.onerror = (event) => {
          console.warn("Could not open purchase orders database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const db = event.target.result;

          // Check if the store exists
          if (!db.objectStoreNames.contains(this.poStoreName)) {
            console.warn("Purchase orders store not found in database");
            db.close();
            resolve([]);
            return;
          }

          const transaction = db.transaction([this.poStoreName], "readonly");
          const store = transaction.objectStore(this.poStoreName);
          const getAllRequest = store.getAll();

          getAllRequest.onsuccess = () => {
            const pos = getAllRequest.result || [];

            // Parse stored date strings back to Date objects (same logic as purchase_order.js)
            pos.forEach(po => {
              try {
                const parseStoredDate = (dateStr) => {
                  if (!dateStr || typeof dateStr !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                    return null;
                  }
                  const date = new Date(`${dateStr}T00:00:00.000Z`);
                  return isNaN(date.getTime()) ? null : date;
                };

                po.date = parseStoredDate(po.date);
                po.lastModified = parseStoredDate(po.lastModified);
                po.promisedDate = parseStoredDate(po.promisedDate);
                po.actualDelivery = parseStoredDate(po.actualDelivery);

                // Ensure total is a number
                po.total = parseFloat(po.total) || 0;
              } catch (dateError) {
                console.warn(`Error parsing stored dates for PO ID ${po.id}:`, dateError);
                po.date = null;
              }
            });

            // Filter out POs with invalid dates
            const validPOs = pos.filter(po => po.date instanceof Date);
            console.log(`Retrieved ${pos.length} POs, returning ${validPOs.length} with valid dates for drill-down`);
            resolve(validPOs);
          };

          getAllRequest.onerror = (event) => {
            console.error("Error retrieving purchase orders for drill-down:", event.target.error);
            resolve([]);
          };

          transaction.oncomplete = () => {
            db.close();
          };

          transaction.onerror = (event) => {
            console.error("Transaction error retrieving POs for drill-down:", event.target.error);
            db.close();
            resolve([]);
          };
        };

        request.onupgradeneeded = (event) => {
          console.warn("Purchase orders database needs upgrade for drill-down");
          event.target.transaction.abort();
          resolve([]);
        };
      } catch (error) {
        console.error("Error in getPurchaseOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  async getPurchaseOrderDetails(monthData, inventoryId, vendorName) {
    // Ensure PO data is loaded
    await this.loadPurchaseOrderData();

    const purchaseDetails = [];

    // Process each purchase in the month
    for (const purchase of monthData) {
      const orderNbr = purchase.orderNbr;

      if (!orderNbr) {
        // If no order number, create a basic detail from price tracker data
        purchaseDetails.push({
          source: 'price_tracker',
          orderNbr: 'Unknown',
          purchaseDate: purchase.purchaseDate,
          inventoryId: inventoryId,
          vendorName: vendorName,
          unitCost: purchase.unitCost,
          currency: purchase.currency || 'USD',
          status: 'Unknown',
          lineItems: []
        });
        continue;
      }

      // Look up the purchase order
      const relatedPOs = this.poData.get(orderNbr) || [];

      if (relatedPOs.length === 0) {
        // No PO found, create basic detail from price tracker data
        purchaseDetails.push({
          source: 'price_tracker',
          orderNbr: orderNbr,
          purchaseDate: purchase.purchaseDate,
          inventoryId: inventoryId,
          vendorName: vendorName,
          unitCost: purchase.unitCost,
          currency: purchase.currency || 'USD',
          status: 'Unknown',
          lineItems: []
        });
        continue;
      }

      // Find the best matching PO (closest date, matching vendor if possible)
      let bestMatch = relatedPOs[0];
      if (relatedPOs.length > 1) {
        bestMatch = relatedPOs.reduce((best, current) => {
          // Prefer POs with matching vendor
          const bestVendorMatch = best.vendor && best.vendor.toLowerCase().includes(vendorName.toLowerCase());
          const currentVendorMatch = current.vendor && current.vendor.toLowerCase().includes(vendorName.toLowerCase());

          if (currentVendorMatch && !bestVendorMatch) {
            return current;
          }
          if (bestVendorMatch && !currentVendorMatch) {
            return best;
          }

          // If vendor match is same, prefer closer date
          const bestDateDiff = Math.abs(best.date?.getTime() - purchase.purchaseDate.getTime()) || Infinity;
          const currentDateDiff = Math.abs(current.date?.getTime() - purchase.purchaseDate.getTime()) || Infinity;

          return currentDateDiff < bestDateDiff ? current : best;
        });
      }

      // Get all line items and mark the clicked one
      const allLineItems = bestMatch.lineItems || [];
      const clickedItemLineNumbers = [];

      // Find all line items that match the clicked inventory ID
      allLineItems.forEach(item => {
        if (item.inventoryId === inventoryId) {
          clickedItemLineNumbers.push(item.lineNbr);
        }
      });

      purchaseDetails.push({
        source: 'purchase_order',
        orderNbr: bestMatch.orderNbr,
        poId: bestMatch.id,
        purchaseDate: purchase.purchaseDate,
        orderDate: bestMatch.date,
        promisedDate: bestMatch.promisedDate,
        inventoryId: inventoryId,
        vendorId: bestMatch.vendorId,
        vendorName: bestMatch.vendor || vendorName,
        unitCost: purchase.unitCost,
        currency: purchase.currency || 'USD',
        status: bestMatch.status,
        description: bestMatch.description,
        terms: bestMatch.terms,
        total: bestMatch.total,
        convertedTotal: bestMatch.convertedTotal,
        convertedCurrency: bestMatch.convertedCurrency,
        allLineItems: allLineItems,
        clickedInventoryId: inventoryId,
        clickedItemLineNumbers: clickedItemLineNumbers
      });
    }

    return purchaseDetails;
  }

  async showPurchaseOrderDetails(inventoryId, vendorName, monthKey) {
    try {
      // Find the month data
      const partKey = `${inventoryId}|${vendorName}`;
      const partData = this.pivotData.find(item =>
        item.inventoryId === inventoryId && item.vendorName === vendorName
      );

      if (!partData || !partData.monthlyPrices[monthKey]) {
        this.showError('Purchase data not found');
        return;
      }

      const monthData = partData.monthlyPrices[monthKey];

      // Show loading state
      this.showPODetailsLoading();

      // Get purchase order details
      const purchaseDetails = await this.getPurchaseOrderDetails(monthData, inventoryId, vendorName);

      // Hide loading and show modal
      this.hidePODetailsLoading();
      this.renderPODetailsModal(purchaseDetails, inventoryId, vendorName, monthKey);

    } catch (error) {
      console.error('Error showing purchase order details:', error);
      this.hidePODetailsLoading();
      this.showError('Failed to load purchase order details: ' + error.message);
    }
  }

  showPODetailsLoading() {
    // Remove any existing modal first
    this.hidePODetailsModal();

    const loadingModal = document.createElement('div');
    loadingModal.id = 'po-details-loading';
    loadingModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center';
    loadingModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <div class="flex items-center space-x-3">
          <div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-gray-700 dark:text-gray-300">Loading purchase order details...</span>
        </div>
      </div>
    `;
    document.body.appendChild(loadingModal);
  }

  hidePODetailsLoading() {
    const loadingModal = document.getElementById('po-details-loading');
    if (loadingModal) {
      loadingModal.remove();
    }
  }

  renderPODetailsModal(purchaseDetails, inventoryId, vendorName, monthKey) {
    // Remove any existing modal first
    this.hidePODetailsModal();

    const modal = document.createElement('div');
    modal.id = 'po-details-modal';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';

    const monthLabel = this.monthColumns.find(col => col.key === monthKey)?.label || monthKey;
    const isMultiple = purchaseDetails.length > 1;

    modal.innerHTML = `
      <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <!-- Modal Header -->
        <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              Purchase Order Details
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Showing ${isMultiple ? 'all purchase orders' : 'purchase order'} for
              <span class="font-medium text-gray-900 dark:text-white">${this.escapeHtml(inventoryId)}</span>
              ${isMultiple ? ` (${purchaseDetails.length} orders found)` : ''}
            </p>
          </div>
          <button id="close-po-modal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 flex-shrink-0 ml-4">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Content -->
        <div class="mt-4">
          <div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Part:</span>
                <span class="ml-2 text-gray-900 dark:text-white">${this.escapeHtml(inventoryId)}</span>
              </div>
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Vendor:</span>
                <span class="ml-2 text-gray-900 dark:text-white">${this.escapeHtml(vendorName)}</span>
              </div>
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Month:</span>
                <span class="ml-2 text-gray-900 dark:text-white">${monthLabel}</span>
              </div>
            </div>
          </div>

          ${isMultiple ?
            `<p class="mb-4 text-sm text-gray-600 dark:text-gray-400">
              Found ${purchaseDetails.length} purchase${purchaseDetails.length > 1 ? 's' : ''} for this month:
            </p>` : ''}

          <!-- Purchase Details -->
          <div class="space-y-4 max-h-96 overflow-y-auto">
            ${purchaseDetails.map((detail, index) => this.renderPurchaseDetail(detail, index)).join('')}
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
          <button id="close-po-modal-btn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors">
            Cancel
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners for closing the modal
    const closeButtons = modal.querySelectorAll('#close-po-modal, #close-po-modal-btn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => this.hidePODetailsModal());
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        this.hidePODetailsModal();
      }
    });

    // Close modal with Escape key
    const escapeHandler = (e) => {
      if (e.key === 'Escape') {
        this.hidePODetailsModal();
        document.removeEventListener('keydown', escapeHandler);
      }
    };
    document.addEventListener('keydown', escapeHandler);
  }

  renderPurchaseDetail(detail, index) {
    const isFromPO = detail.source === 'purchase_order';
    const hasLineItems = detail.lineItems && detail.lineItems.length > 0;

    return `
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 ${index > 0 ? 'mt-4' : ''}">
        <!-- Purchase Header -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-3">
            <h4 class="font-medium text-gray-900 dark:text-white">
              PO #${this.escapeHtml(detail.orderNbr)}
            </h4>
            ${isFromPO ?
              `<span class="px-2 py-1 text-xs font-medium rounded-full ${this.getPOStatusClass(detail.status)}">
                ${this.escapeHtml(detail.status)}
              </span>` :
              `<span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                Limited Data
              </span>`
            }
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            ${this.formatDate(detail.purchaseDate)}
          </div>
        </div>

        <!-- Purchase Details Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm mb-4">
          <div>
            <span class="font-medium text-gray-700 dark:text-gray-300">Unit Cost:</span>
            <div class="mt-1 text-gray-900 dark:text-white">
              ${this.formatAmount(detail.unitCost, detail.currency)}
              ${this.currencyConversion.enabled && detail.currency === 'USD' && this.currencyConversion.toCurrency === 'CAD' ?
                `<div class="text-xs text-gray-500 dark:text-gray-400">
                  (${this.formatAmount(detail.unitCost * this.currencyConversion.rate, this.currencyConversion.toCurrency)} converted)
                </div>` : ''}
            </div>
          </div>

          ${isFromPO ? `
            <div>
              <span class="font-medium text-gray-700 dark:text-gray-300">Order Date:</span>
              <div class="mt-1 text-gray-900 dark:text-white">${this.formatDate(detail.orderDate)}</div>
            </div>

            ${detail.promisedDate ? `
              <div>
                <span class="font-medium text-gray-700 dark:text-gray-300">Promised Date:</span>
                <div class="mt-1 text-gray-900 dark:text-white">${this.formatDate(detail.promisedDate)}</div>
              </div>
            ` : ''}

            <div>
              <span class="font-medium text-gray-700 dark:text-gray-300">PO Total:</span>
              <div class="mt-1 text-gray-900 dark:text-white">
                ${detail.convertedTotal ?
                  this.formatAmount(detail.convertedTotal, detail.convertedCurrency) :
                  this.formatAmount(detail.total, detail.currency)}
                ${detail.convertedTotal && detail.currency !== detail.convertedCurrency ?
                  `<div class="text-xs text-gray-500 dark:text-gray-400">
                    (${this.formatAmount(detail.total, detail.currency)} original)
                  </div>` : ''}
              </div>
            </div>

            ${detail.description ? `
              <div class="md:col-span-2 lg:col-span-3">
                <span class="font-medium text-gray-700 dark:text-gray-300">Description:</span>
                <div class="mt-1 text-gray-900 dark:text-white">${this.escapeHtml(detail.description)}</div>
              </div>
            ` : ''}
          ` : ''}
        </div>

        <!-- All Line Items (if available) -->
        ${detail.allLineItems && detail.allLineItems.length > 0 ? `
          <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
            <h5 class="font-medium text-gray-900 dark:text-white mb-2">
              Complete Purchase Order Line Items:
              <span class="text-sm font-normal text-gray-600 dark:text-gray-400">
                (${detail.allLineItems.length} total items)
              </span>
            </h5>
            <div class="overflow-x-auto">
              <table class="min-w-full text-sm line-items-table">
                <thead>
                  <tr>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Line #</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Part #</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Cost</th>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Extended</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  ${detail.allLineItems.map(item => {
                    const isClickedItem = item.inventoryId === detail.clickedInventoryId;
                    const rowClass = isClickedItem ? 'line-item-highlighted' : '';

                    return `
                      <tr class="${rowClass}">
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div class="flex items-center">
                            ${item.lineNbr}
                            ${isClickedItem ? '<span class="ml-2 clicked-item-indicator text-blue-600 dark:text-blue-400 text-lg">●</span>' : ''}
                          </div>
                        </td>
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div>
                            <div class="font-medium ${isClickedItem ? 'text-blue-600 dark:text-blue-400' : ''}">${this.escapeHtml(item.inventoryId)}</div>
                            ${isClickedItem ? '<div class="text-xs text-blue-600 dark:text-blue-400 font-normal mt-1">← You clicked this item</div>' : ''}
                          </div>
                        </td>
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div class="max-w-xs truncate" title="${this.escapeHtml(item.description || 'No description')}">
                            ${this.escapeHtml(item.description || 'No description')}
                          </div>
                        </td>
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div class="font-medium">${item.quantity}</div>
                          ${item.uom ? `<div class="text-xs text-gray-500 dark:text-gray-400">${item.uom}</div>` : ''}
                        </td>
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div class="font-medium">${this.formatAmount(item.unitCost, detail.currency)}</div>
                        </td>
                        <td class="px-3 py-3 text-gray-900 dark:text-white">
                          <div class="font-medium">${this.formatAmount(item.extendedCost, detail.currency)}</div>
                        </td>
                      </tr>
                    `;
                  }).join('')}
                </tbody>
              </table>
            </div>

            <!-- Summary row for total -->
            <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex justify-between items-center text-sm">
                <span class="font-medium text-gray-700 dark:text-gray-300">
                  Purchase Order Total:
                </span>
                <span class="font-bold text-gray-900 dark:text-white">
                  ${detail.convertedTotal ?
                    this.formatAmount(detail.convertedTotal, detail.convertedCurrency) :
                    this.formatAmount(detail.total, detail.currency)}
                  ${detail.convertedTotal && detail.currency !== detail.convertedCurrency ?
                    `<div class="text-xs text-gray-500 dark:text-gray-400">
                      (${this.formatAmount(detail.total, detail.currency)} original)
                    </div>` : ''}
                </span>
              </div>
            </div>
          </div>
        ` : ''}
      </div>
    `;
  }

  getPOStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'received':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  }

  hidePODetailsModal() {
    const modal = document.getElementById('po-details-modal');
    if (modal) {
      modal.remove();
    }
  }

  showSettingsModal() {
    // Remove any existing modal first
    const existingModal = document.getElementById('settings-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Available currencies (same as purchase_order.js)
    const currencies = ['USD', 'CAD', 'EUR', 'GBP', 'AUD', 'JPY'];

    const settingsHtml = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4">
        <div class="p-6">
          <h3 class="text-lg font-medium mb-4">Price Tracker Settings</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Settings Column -->
            <div>
              <h4 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Basic Settings</h4>

              <div class="mb-4">
                <label class="block text-sm font-medium mb-1">Items per page</label>
                <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                  <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
                  <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
                  <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
                  <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
                </select>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium mb-1">Default sort</label>
                <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                  <option value="inventoryId" ${this.sortField === 'inventoryId' ? 'selected' : ''}>Inventory ID</option>
                  <option value="vendorName" ${this.sortField === 'vendorName' ? 'selected' : ''}>Vendor</option>
                  <option value="isStock" ${this.sortField === 'isStock' ? 'selected' : ''}>Type</option>
                </select>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium mb-1">Sort direction</label>
                <div class="flex gap-4">
                  <label class="inline-flex items-center">
                    <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
                    Ascending
                  </label>
                  <label class="inline-flex items-center">
                    <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
                    Descending
                  </label>
                </div>
              </div>
            </div>

            <!-- Display Settings Column -->
            <div>
              <h4 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Display Options</h4>

              <div class="space-y-3">
                <label class="flex items-center">
                  <input type="checkbox" id="settings-show-currency" ${this.displaySettings.showCurrency ? 'checked' : ''} class="mr-3">
                  <span class="text-sm">Show currency symbols in prices</span>
                </label>

                <label class="flex items-center">
                  <input type="checkbox" id="settings-show-usd-amounts" ${this.displaySettings.showUsdAmounts ? 'checked' : ''} class="mr-3">
                  <span class="text-sm">Show original USD amounts when converted</span>
                </label>

                <label class="flex items-center">
                  <input type="checkbox" id="settings-show-purchase-counts" ${this.displaySettings.showPurchaseCounts ? 'checked' : ''} class="mr-3">
                  <span class="text-sm">Show purchase counts (e.g., "2 purchases")</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Currency Conversion Settings -->
          <div class="mt-6 border-t pt-4">
            <div class="flex items-center mb-3">
              <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">Currency Conversion (USD → CAD)</h4>
              <div class="ml-auto">
                <label class="inline-flex items-center">
                  <input type="checkbox" id="settings-currency-enabled" ${this.currencyConversion.enabled ? 'checked' : ''} class="mr-2">
                  <span class="text-sm">Convert USD prices to CAD</span>
                </label>
              </div>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 mb-3">
              Only USD prices will be converted to CAD. CAD prices remain unchanged.
            </p>

            <div>
              <label class="block text-sm font-medium mb-1">USD to CAD Conversion Rate</label>
              <div class="flex items-center gap-2 max-w-md">
                <span class="text-sm whitespace-nowrap">1 USD =</span>
                <input type="number" step="0.01" min="0.01" id="settings-currency-rate" value="${this.currencyConversion.rate}" class="w-24 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
                <span class="text-sm whitespace-nowrap">CAD</span>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Current rate: 1 USD = ${this.currencyConversion.rate} CAD
              </p>
            </div>
          </div>

          <div class="flex justify-end gap-2 mt-6 pt-4 border-t">
            <button id="settings-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="settings-save" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Save Changes
            </button>
          </div>
        </div>
      </div>
    `;

    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal';

    const modalContent = document.createElement('div');
    modalContent.innerHTML = settingsHtml;

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // No currency dropdown event listeners needed since we fixed USD to CAD

    // Setup event listeners for the modal
    const cancelButton = document.getElementById('settings-cancel');
    const saveButton = document.getElementById('settings-save');

    // Keyboard event handler
    let keydownHandler = null;

    const closeModal = () => {
      try {
        // Remove keyboard event listener
        if (keydownHandler) {
          document.removeEventListener('keydown', keydownHandler);
          keydownHandler = null;
        }

        // Remove modal from DOM
        const modal = document.getElementById('settings-modal');
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      } catch (error) {
        console.warn("Error closing settings modal:", error);
      }
    };

    if (cancelButton) {
      cancelButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      });
    }

    if (saveButton) {
      saveButton.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Get basic settings
        const itemsPerPage = parseInt(document.getElementById('settings-items-per-page').value);
        const defaultSort = document.getElementById('settings-default-sort').value;

        // Get sort direction
        let selectedDirection = 'asc';
        const directionRadios = document.querySelectorAll('input[name="settings-sort-direction"]');
        directionRadios.forEach(radio => {
          if (radio.checked) {
            selectedDirection = radio.value;
          }
        });

        // Get currency conversion settings
        const currencyEnabled = document.getElementById('settings-currency-enabled');
        const currencyRate = document.getElementById('settings-currency-rate');

        // Get display settings
        const showCurrency = document.getElementById('settings-show-currency');
        const showUsdAmounts = document.getElementById('settings-show-usd-amounts');
        const showPurchaseCounts = document.getElementById('settings-show-purchase-counts');

        // Update settings
        const oldSettings = { ...this.currencyConversion };
        const oldDisplaySettings = { ...this.displaySettings };

        this.itemsPerPage = itemsPerPage;
        this.sortField = defaultSort;
        this.sortDirection = selectedDirection;

        if (currencyEnabled) {
          this.currencyConversion.enabled = currencyEnabled.checked;
        }

        // Force USD to CAD conversion only
        this.currencyConversion.fromCurrency = 'USD';
        this.currencyConversion.toCurrency = 'CAD';

        if (currencyRate) {
          const rate = parseFloat(currencyRate.value);
          if (!isNaN(rate) && rate > 0) {
            this.currencyConversion.rate = rate;
          }
        }

        // Update display settings
        if (showCurrency) {
          this.displaySettings.showCurrency = showCurrency.checked;
        }

        if (showUsdAmounts) {
          this.displaySettings.showUsdAmounts = showUsdAmounts.checked;
        }

        if (showPurchaseCounts) {
          this.displaySettings.showPurchaseCounts = showPurchaseCounts.checked;
        }

        // Check if settings changed
        const settingsChanged =
          oldSettings.enabled !== this.currencyConversion.enabled ||
          oldSettings.fromCurrency !== this.currencyConversion.fromCurrency ||
          oldSettings.toCurrency !== this.currencyConversion.toCurrency ||
          oldSettings.rate !== this.currencyConversion.rate ||
          oldDisplaySettings.showCurrency !== this.displaySettings.showCurrency ||
          oldDisplaySettings.showUsdAmounts !== this.displaySettings.showUsdAmounts ||
          oldDisplaySettings.showPurchaseCounts !== this.displaySettings.showPurchaseCounts;

        // Save settings to IndexedDB
        await this.saveSettings();

        // Reset to first page and re-render
        this.currentPage = 1;
        this.calculateTotalPages();
        this.applyFilters();
        this.render();
        this.setupEventListeners();

        // Close modal
        closeModal();

        // Show success message
        if (settingsChanged) {
          this.showSuccess("Settings updated successfully");
        } else {
          this.showSuccess("Settings saved successfully");
        }
      });
    }

    // Close modal when clicking outside
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        e.preventDefault();
        e.stopPropagation();
        closeModal();
      }
    });

    // Close modal with Escape key
    keydownHandler = (e) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        closeModal();
      }
    };
    document.addEventListener('keydown', keydownHandler);
  }

  showLoading() {
    this.isLoading = true;
  }

  hideLoading() {
    this.isLoading = false;
  }

  showError(message) {
    console.error('Price Tracker Error:', message);

    // Try to use notification system if available
    try {
      if (this.notificationSystem && typeof this.notificationSystem.show === 'function') {
        this.notificationSystem.show(message, 'error');
      } else {
        // Fallback to simple notification
        this.showSimpleNotification(message, 'error');
      }
    } catch (error) {
      console.error('Error showing notification:', error);
      // Final fallback
      this.showSimpleNotification(message, 'error');
    }
  }

  showSuccess(message) {
    console.log('Price Tracker Success:', message);

    try {
      if (this.notificationSystem && typeof this.notificationSystem.show === 'function') {
        this.notificationSystem.show(message, 'success');
      } else {
        this.showSimpleNotification(message, 'success');
      }
    } catch (error) {
      console.error('Error showing notification:', error);
      this.showSimpleNotification(message, 'success');
    }
  }

  showSimpleNotification(message, type = 'info') {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
      type === 'error' ? 'bg-red-500 text-white' :
      type === 'success' ? 'bg-green-500 text-white' :
      'bg-blue-500 text-white'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);
  }

  async loadSettings() {
    try {
      if (!this.db) {
        console.log("Database not ready, using default settings");
        return;
      }

      const transaction = this.db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);

      // Get currency settings
      const currencySettings = await new Promise((resolve) => {
        const request = store.get("currencySettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading currency settings:", event.target.error);
          resolve(null);
        };
      });

      if (currencySettings) {
        console.log("Loaded currency settings:", currencySettings);
        this.currencyConversion = currencySettings.value;
      } else {
        console.log("No saved currency settings, using defaults");
      }

      // Get display settings
      const displaySettings = await new Promise((resolve) => {
        const request = store.get("displaySettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading display settings:", event.target.error);
          resolve(null);
        };
      });

      if (displaySettings) {
        console.log("Loaded display settings:", displaySettings);
        // Merge with defaults to ensure all properties exist
        this.displaySettings = {
          showCurrency: true,
          showUsdAmounts: true,
          showPurchaseCounts: true,
          ...displaySettings.value
        };
      } else {
        console.log("No saved display settings, using defaults");
      }

    } catch (error) {
      console.error("Error loading settings:", error);
      // Continue with defaults if settings can't be loaded
    }
  }

  async saveSettings() {
    try {
      if (!this.db) {
        console.log("Database not ready, cannot save settings");
        return;
      }

      const transaction = this.db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);

      // Save currency settings
      store.put({
        id: "currencySettings",
        value: this.currencyConversion
      });

      // Save display settings
      store.put({
        id: "displaySettings",
        value: this.displaySettings
      });

      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          console.log("Settings transaction completed successfully");
          resolve();
        };
        transaction.onerror = (event) => {
          console.error("Settings transaction error:", event.target.error);
          reject(event.target.error);
        };
      });

    } catch (error) {
      console.error("Error saving settings:", error);
    }
  }

  // Test methods for debugging
  testChartToggle(inventoryId, vendorName) {
    const partKey = `${inventoryId}|${vendorName}`;
    console.log('Testing chart toggle for:', partKey);
    console.log('Current expanded charts:', Array.from(this.expandedCharts));
    console.log('Available data:', this.filteredData.slice(0, 5).map(r => `${r.inventoryId}|${r.vendorName}`));
    this.toggleChart(partKey);
    console.log('After toggle expanded charts:', Array.from(this.expandedCharts));
  }

  // Debug currency data
  debugCurrencyData() {
    console.log('=== CURRENCY DEBUG ===');
    console.log('Raw data sample:', this.partHistoryData.slice(0, 10).map(item => ({
      inventoryId: item.inventoryId,
      unitCost: item.unitCost,
      currency: item.currency
    })));

    console.log('Processed pivot data sample:', this.filteredData.slice(0, 5).map(row => ({
      inventoryId: row.inventoryId,
      monthlyPrices: Object.keys(row.monthlyPrices).slice(0, 2).map(monthKey => ({
        month: monthKey,
        prices: row.monthlyPrices[monthKey].map(p => ({
          unitCost: p.unitCost,
          currency: p.currency
        }))
      }))
    })));

    console.log('Currency conversion settings:', this.currencyConversion);
    console.log('=== END CURRENCY DEBUG ===');
  }

  // Force re-render for testing
  forceRender() {
    console.log('Force rendering...');
    this.render();
    this.setupEventListeners();
  }

  // Cleanup method
  destroy() {
    // Clean up all chart instances
    this.chartInstances.forEach((chart, partKey) => {
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
    });
    this.chartInstances.clear();
    this.expandedCharts.clear();

    if (this.db) {
      this.db.close();
      this.db = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }

    // Clear any timeouts
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }
}
