// Sales Order Weekly Metrics component for MRP Dashboard
import { AllocationUtils } from './allocation_utils.js';

export class SalesOrderMetrics {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.salesOrders = [];
    this.weeklyMetrics = [];
    this.weeks = [];
    this.isLoading = false;

    // Database configuration for inventory (for cost and quantity analysis)
    this.inventoryDbName = 'inventoryDb';
    this.inventoryStoreName = 'inventoryItems';
    this.inventoryItems = []; // Cache for inventory data

    // Database configuration for production orders (for WIP calculations)
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';
    this.productionOrders = []; // Cache for production data

    // Shared allocation utility for component breakdown
    this.allocationUtils = new AllocationUtils();

    // Week configuration
    this.startDate = new Date(2025, 5, 30); // June 30, 2025 (month is 0-indexed)
    this.endDate = new Date(2025, 11, 29); // December 29, 2025

    // Today's date for past due calculations - use actual current date
    this.todayDate = new Date(); // Current date for dynamic week shifting

    // Current week information for dynamic shifting
    this.currentWeekInfo = null;
    this.pastDueCutoffDate = null;
    
    // Initialize display settings with defaults
    this.displaySettings = {
      showCurrency: true // Default to showing currency symbols
    };
    
    // Past due metrics
    this.pastDueMetrics = {
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0  // Add project COGs for past due
    };
  }

  // Method to update display settings (called by parent component)
  updateDisplaySettings(newSettings) {
    if (newSettings) {
      this.displaySettings = { ...this.displaySettings, ...newSettings };
      console.log("Updated display settings in SalesOrderMetrics:", this.displaySettings);
    }
  }

  async init() {
    console.log("Initializing Sales Order Weekly Metrics component");
    
    // Inherit display settings from parent component if available
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Inherited display settings from parent:", this.displaySettings);
    }
    
    this.isLoading = true;
    this.render();

    try {
      // Load supporting data first
      await this.loadInventoryItems();
      await this.loadProductionOrders();

      // Initialize allocation utilities with loaded data
      this.allocationUtils.setData(this.productionOrders, this.inventoryItems);

      // Get sales orders data from parent component
      this.salesOrders = this.parentComponent.salesOrders || [];
      
      // Generate weeks and calculate metrics
      this.generateWeeks();
      this.calculateWeeklyMetrics();
      
      this.isLoading = false;
      this.render();
    } catch (error) {
      console.error("Error initializing sales order weekly metrics:", error);
      this.isLoading = false;
      this.showError("Failed to initialize weekly metrics: " + error.message);
    }
  }

  async loadInventoryItems() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.inventoryDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open inventory database for metrics:", event.target.error);
        this.inventoryItems = [];
        resolve(); // Continue without inventory data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.inventoryStoreName)) {
          console.warn("Inventory store not found for metrics");
          this.inventoryItems = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.inventoryStoreName], "readonly");
        const store = transaction.objectStore(this.inventoryStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.inventoryItems = getAllRequest.result;
          console.log(`Loaded ${this.inventoryItems.length} inventory items for metrics`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading inventory items for metrics:", event.target.error);
          this.inventoryItems = [];
          resolve(); // Continue without inventory data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadProductionOrders() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.productionDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open production database for metrics:", event.target.error);
        this.productionOrders = [];
        resolve(); // Continue without production data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.productionStoreName)) {
          console.warn("Production store not found for metrics");
          this.productionOrders = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.productionStoreName], "readonly");
        const store = transaction.objectStore(this.productionStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.productionOrders = getAllRequest.result;
          console.log(`Loaded ${this.productionOrders.length} production orders for metrics`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading production orders for metrics:", event.target.error);
          this.productionOrders = [];
          resolve(); // Continue without production data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  generateWeeks() {
    console.log("Generating weeks from June 30, 2025 to December 29, 2025 with dynamic shifting");

    this.weeks = [];
    const currentDate = new Date(this.startDate);

    // First, generate all weeks from start to end date
    const allWeeks = [];
    while (currentDate <= this.endDate) {
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 6); // Add 6 days to get week end

      // Format week label
      const weekLabel = `${this.formatDateShort(weekStart)} - ${this.formatDateShort(weekEnd)}`;

      allWeeks.push({
        id: `week-${allWeeks.length + 1}`,
        label: weekLabel,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        originalWeekNumber: allWeeks.length + 1
      });

      // Move to next week (add 7 days)
      currentDate.setDate(currentDate.getDate() + 7);
    }

    // Find current week based on today's date
    this.currentWeekInfo = this.findCurrentWeek(allWeeks);

    if (this.currentWeekInfo) {
      console.log(`Current week found: Week ${this.currentWeekInfo.originalWeekNumber} (${this.currentWeekInfo.label})`);

      // Set past due cutoff to the start of current week
      this.pastDueCutoffDate = new Date(this.currentWeekInfo.startDate);

      // Filter weeks to only include current week and future weeks
      const futureWeeks = allWeeks.filter(week => week.startDate >= this.currentWeekInfo.startDate);

      // Renumber weeks starting from 1 for current week
      this.weeks = futureWeeks.map((week, index) => ({
        ...week,
        id: `week-${index + 1}`,
        weekNumber: index + 1
      }));

      console.log(`Dynamic week shifting applied: ${this.weeks.length} weeks from current week forward`);
      console.log(`Past due cutoff set to: ${this.formatDateShort(this.pastDueCutoffDate)}`);
    } else {
      // Fallback: use all weeks if current week not found
      console.warn("Current week not found in range, using all weeks");
      this.weeks = allWeeks.map((week, index) => ({
        ...week,
        weekNumber: index + 1
      }));
      this.pastDueCutoffDate = new Date(this.todayDate);
    }

    console.log(`Generated ${this.weeks.length} weeks for analysis`);
  }

  findCurrentWeek(allWeeks) {
    // Find the week that contains today's date
    for (let week of allWeeks) {
      if (this.todayDate >= week.startDate && this.todayDate <= week.endDate) {
        return week;
      }
    }

    // If today is before the first week, return the first week
    if (this.todayDate < allWeeks[0].startDate) {
      return allWeeks[0];
    }

    // If today is after the last week, return null (will use fallback)
    return null;
  }

  calculateWeeklyMetrics() {
    console.log("Calculating weekly metrics for", this.salesOrders.length, "sales orders");
    
    // Initialize weekly metrics
    this.weeklyMetrics = this.weeks.map(week => ({
      week: week,
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0,
      cumulativeCogs: 0
    }));

    // Calculate past due order counts and totals first
    this.calculatePastDueOrdersOnly();

    // Use global allocation for ALL sales orders (same approach as sales_order_usage.js)
    console.log("Running global allocation for all sales orders to ensure consistent allocation...");
    const allSalesOrders = this.salesOrders || [];
    const globalAllocationResult = this.allocationUtils.allocateComponentsGlobally(
      allSalesOrders, 
      this.inventoryItems
    );

    // Process each sales order using results from global allocation
    this.salesOrders.forEach(order => {
      const weekIndex = this.getWeekIndexForOrder(order);
      
      if (weekIndex !== -1) {
        const weekMetric = this.weeklyMetrics[weekIndex];
        
        // Add order to this week
        weekMetric.orders.push(order);
        weekMetric.ordersCount++;
        weekMetric.orderTotal += order.OrderTotal || 0;
        
        // Filter allocations for this specific sales order from global allocation
        const orderAllocations = globalAllocationResult.allocations.filter(alloc => 
          alloc.OrderNbr === order.OrderNbr
        );
        
        // Group by InventoryID to consolidate multiple line items for same part
        const consolidatedItems = new Map();
        
        orderAllocations.forEach(allocation => {
          const key = allocation.InventoryID;
          
          if (consolidatedItems.has(key)) {
            const existing = consolidatedItems.get(key);
            existing.QtyRequired += allocation.QtyRequired;
            existing.QtyAllocated += allocation.QtyAllocated;
            existing.QtyShort += allocation.QtyShort;
          } else {
            consolidatedItems.set(key, {
              InventoryID: allocation.InventoryID,
              Description: allocation.Description,
              QtyRequired: allocation.QtyRequired,
              QtyAllocated: allocation.QtyAllocated,
              QtyShort: allocation.QtyShort,
              LastCost: allocation.LastCost,
              UOM: allocation.UOM
            });
          }
        });
        
        const consolidatedArray = Array.from(consolidatedItems.values());
        
        // Calculate totals from global allocation results (same logic as sales_order_usage.js)
        const projectCost = consolidatedArray.reduce((sum, item) => sum + (item.QtyRequired * item.LastCost), 0);
        const onHandValue = consolidatedArray.reduce((sum, item) => sum + (item.QtyAllocated * item.LastCost), 0);
        const missingValue = consolidatedArray.reduce((sum, item) => sum + (item.QtyShort * item.LastCost), 0);
        
        weekMetric.projectCogs += projectCost;
        weekMetric.onHandValue += onHandValue;
        weekMetric.missingValue += missingValue;
        
        console.log(`📊 Week ${weekMetric.week.weekNumber} - Order ${order.OrderNbr}: Project=${projectCost.toFixed(2)}, OnHand=${onHandValue.toFixed(2)}, Missing=${missingValue.toFixed(2)}`);
      }
    });

    // Past Due WIP Value should be the sum of ALL order totals (Past Due + ALL weekly totals)
    // This is the complete total pipeline value
    this.pastDueMetrics.wipValue = this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, week) => sum + week.orderTotal, 0);

    // Now calculate cascading WIP Values for each week
    // Week 1 WIP Value = Past Due WIP Value - Past Due Order Total
    // Week N WIP Value = Previous Week WIP Value - Previous Week Order Total
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index === 0) {
        // Week 1: Base on Past Due
        weekMetric.wipValue = this.pastDueMetrics.wipValue - this.pastDueMetrics.orderTotal;
      } else {
        // Week N: Base on previous week
        const prevWeek = this.weeklyMetrics[index - 1];
        weekMetric.wipValue = prevWeek.wipValue - prevWeek.orderTotal;
      }
    });

    // Calculate Weekly WIP as the difference between current and NEXT WIP values
    // Past Due Weekly WIP = Past Due WIP Value - Week 1 WIP Value
    // Week N Weekly WIP = Week N WIP Value - Week (N+1) WIP Value
    
    // First calculate Past Due Weekly WIP
    if (this.weeklyMetrics.length > 0) {
      this.pastDueMetrics.weeklyWip = this.pastDueMetrics.wipValue - this.weeklyMetrics[0].wipValue;
    } else {
      this.pastDueMetrics.weeklyWip = 0;
    }
    
    // Then calculate each week's Weekly WIP
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index < this.weeklyMetrics.length - 1) {
        // Week N: Difference from next week
        const nextWeek = this.weeklyMetrics[index + 1];
        weekMetric.weeklyWip = weekMetric.wipValue - nextWeek.wipValue;
      } else {
        // Last week: WIP Value itself (no next week to subtract)
        weekMetric.weeklyWip = weekMetric.wipValue;
      }
    });

    // Calculate cascading Total Cumulative COGs (sum from Past Due Project COGs)
    // Past Due Total Cumulative COGs = Past Due Total Project COGs
    // Week 1 Total Cumulative COGs = Past Due Total Cumulative COGs + Week 1 Total Project COGs
    // Week N Total Cumulative COGs = Week (N-1) Total Cumulative COGs + Week N Total Project COGs
    this.weeklyMetrics.forEach((weekMetric, index) => {
      if (index === 0) {
        // Week 1: Base on Past Due
        weekMetric.cumulativeCogs = this.pastDueMetrics.projectCogs + weekMetric.projectCogs;
      } else {
        // Week N: Base on previous week
        const prevWeek = this.weeklyMetrics[index - 1];
        weekMetric.cumulativeCogs = prevWeek.cumulativeCogs + weekMetric.projectCogs;
      }
    });

    console.log("Weekly metrics calculated:", this.weeklyMetrics.length, "weeks processed");
    console.log("Past due metrics calculated:", this.pastDueMetrics.ordersCount, "past due orders");

    // Log dynamic week shifting information
    if (this.currentWeekInfo) {
      console.log(`📅 Dynamic Week Shifting Applied:`);
      console.log(`   Current Week: Week ${this.currentWeekInfo.originalWeekNumber} → Week 1`);
      console.log(`   Past Due Cutoff: ${this.formatDateShort(this.pastDueCutoffDate)}`);
      console.log(`   Weeks Displayed: ${this.weeks.length} (from current week forward)`);
    }
  }

  calculatePastDueOrdersOnly() {
    // Reset past due metrics
    this.pastDueMetrics = {
      orderTotal: 0,
      weeklyWip: 0,
      wipValue: 0,
      onHandValue: 0,
      missingValue: 0,
      ordersCount: 0,
      orders: [],
      projectCogs: 0  // Add project COGs for past due
    };

    const cutoffDate = this.pastDueCutoffDate || this.todayDate;
    console.log("Calculating past due orders. Cutoff date:", cutoffDate);
    console.log("Current week info:", this.currentWeekInfo ? `Week ${this.currentWeekInfo.originalWeekNumber}` : 'Not found');

    // Find all orders with shipping date before the cutoff date (start of current week)
    this.salesOrders.forEach((order, index) => {
      if (this.isOrderPastDue(order)) {
        this.pastDueMetrics.orders.push(order);
        this.pastDueMetrics.ordersCount++;
        this.pastDueMetrics.orderTotal += order.OrderTotal || 0;
      }
    });
    
    // Calculate on hand and missing values for past due orders using consistent global allocation
    if (this.pastDueMetrics.orders.length > 0) {
      console.log("Calculating past due on hand and missing values using global allocation...");
      
      // Use global allocation for past due orders only to get proper allocation
      const pastDueAllocationResult = this.allocationUtils.allocateComponentsGlobally(
        this.pastDueMetrics.orders,
        this.inventoryItems
      );
      
      // Group by InventoryID to consolidate multiple line items for same part
      const consolidatedItems = new Map();
      
      pastDueAllocationResult.allocations.forEach(allocation => {
        const key = allocation.InventoryID;
        
        if (consolidatedItems.has(key)) {
          const existing = consolidatedItems.get(key);
          existing.QtyRequired += allocation.QtyRequired;
          existing.QtyAllocated += allocation.QtyAllocated;
          existing.QtyShort += allocation.QtyShort;
        } else {
          consolidatedItems.set(key, {
            InventoryID: allocation.InventoryID,
            Description: allocation.Description,
            QtyRequired: allocation.QtyRequired,
            QtyAllocated: allocation.QtyAllocated,
            QtyShort: allocation.QtyShort,
            LastCost: allocation.LastCost,
            UOM: allocation.UOM
          });
        }
      });
      
      const consolidatedArray = Array.from(consolidatedItems.values());
      
      // Calculate totals from global allocation results (same logic as sales_order_usage.js)
      this.pastDueMetrics.projectCogs = consolidatedArray.reduce((sum, item) => sum + (item.QtyRequired * item.LastCost), 0);
      this.pastDueMetrics.onHandValue = consolidatedArray.reduce((sum, item) => sum + (item.QtyAllocated * item.LastCost), 0);
      this.pastDueMetrics.missingValue = consolidatedArray.reduce((sum, item) => sum + (item.QtyShort * item.LastCost), 0);
    }
    
    console.log("Past due calculation complete:", {
      pastDueCount: this.pastDueMetrics.ordersCount,
      pastDueOrderTotal: this.pastDueMetrics.orderTotal,
      pastDueProjectCogs: this.pastDueMetrics.projectCogs,
      pastDueOnHandValue: this.pastDueMetrics.onHandValue,
      pastDueMissingValue: this.pastDueMetrics.missingValue
    });
  }

  isOrderPastDue(order) {
    if (!order.ShippingDate || !order.ShippingDate.year) {
      return false; // No shipping date, not past due
    }

    try {
      const shipDate = new Date(
        order.ShippingDate.year,
        order.ShippingDate.month - 1, // month is 0-indexed in JS Date
        order.ShippingDate.day
      );

      // Check if shipping date is before the past due cutoff date (start of current week)
      const cutoffDate = this.pastDueCutoffDate || this.todayDate;
      return shipDate < cutoffDate;
    } catch (error) {
      console.error("Error parsing shipping date for past due check", order.OrderNbr, error);
      return false;
    }
  }

  getWeekIndexForOrder(order) {
    if (!order.ShippingDate || !order.ShippingDate.year) {
      return -1; // No shipping date
    }

    try {
      const shipDate = new Date(
        order.ShippingDate.year,
        order.ShippingDate.month - 1, // month is 0-indexed in JS Date
        order.ShippingDate.day
      );

      // Find which week this date falls into
      for (let i = 0; i < this.weeks.length; i++) {
        const week = this.weeks[i];
        if (shipDate >= week.startDate && shipDate <= week.endDate) {
          return i;
        }
      }
    } catch (error) {
      console.error("Error parsing shipping date for order", order.OrderNbr, error);
    }

    return -1; // Date doesn't fall in our week range
  }

  calculateOrderMetrics(order) {
    // DEPRECATED: This method is kept for compatibility but should not be used
    // Use global allocation approach in calculateWeeklyMetrics() instead for consistency
    // Get flat items summary (similar to SalesOrderUsage logic)
    const flatItems = this.buildFlatItemsSummary(order);
    
    let wipValue = 0;
    let onHandValue = 0;
    let missingValue = 0;

    flatItems.forEach(item => {
      const itemTotalValue = item.totalQuantity * item.lastCost;
      const itemOnHandValue = item.qtyOnHand * item.lastCost;
      const itemMissingValue = item.missing * item.lastCost;

      // For WIP calculation, we'll consider items that are in production
      // For now, we'll calculate WIP as the difference between total needed and on hand
      const itemWipValue = Math.max(0, itemTotalValue - itemOnHandValue - itemMissingValue);

      wipValue += itemWipValue;
      onHandValue += itemOnHandValue;
      missingValue += itemMissingValue;
    });

    return {
      wipValue,
      onHandValue,
      missingValue
    };
  }

  buildFlatItemsSummary(salesOrder) {
    // DEPRECATED: This method is kept for compatibility but should not be used
    // The single-order allocation approach here gives different results than global allocation
    // Use the global allocation approach in calculateWeeklyMetrics() instead for consistency
    console.log(`📊 Building flat items summary for order: ${salesOrder.OrderNbr} using global allocation`);
    
    // Use global allocation to get component breakdown for this specific order
    const allocationResult = this.allocationUtils.allocateComponentsGlobally(
      [salesOrder], // Single order for this calculation - THIS IS THE PROBLEM!
      this.inventoryItems
    );
    
    // Group by InventoryID to consolidate multiple line items for same part
    const consolidatedItems = new Map();
    
    allocationResult.allocations.forEach(allocation => {
      const key = allocation.InventoryID;
      
      if (consolidatedItems.has(key)) {
        const existing = consolidatedItems.get(key);
        existing.totalQuantity += allocation.QtyRequired;
        existing.qtyOnHand = allocation.QtyOnHand; // Use global inventory value
        existing.missing = Math.max(0, existing.totalQuantity - existing.qtyOnHand);
      } else {
        consolidatedItems.set(key, {
          inventoryId: allocation.InventoryID,
          description: allocation.Description,
          totalQuantity: allocation.QtyRequired,
          lastCost: allocation.LastCost,
          qtyOnHand: allocation.QtyOnHand,
          missing: Math.max(0, allocation.QtyRequired - allocation.QtyOnHand),
          uom: allocation.UOM,
          found: true // Items from global allocation are found
        });
      }
    });
    
    // Convert Map to array and sort by inventory ID
    const flatItemsArray = Array.from(consolidatedItems.values())
      .sort((a, b) => a.inventoryId.localeCompare(b.inventoryId));
    
    console.log(`📊 Found ${flatItemsArray.length} unique flat items for order ${salesOrder.OrderNbr} from global allocation`);
    return flatItemsArray;
  }

  getInventoryInfo(inventoryId) {
    if (!inventoryId || !this.inventoryItems.length) {
      return {
        lastCost: 0,
        qtyOnHand: 0,
        found: false
      };
    }

    const inventoryItem = this.inventoryItems.find(item => 
      item.InventoryID === inventoryId.trim()
    );

    if (inventoryItem) {
      return {
        lastCost: inventoryItem.LastCost || 0,
        qtyOnHand: inventoryItem.QtyOnHand || 0,
        found: true
      };
    }

    return {
      lastCost: 0,
      qtyOnHand: 0,
      found: false
    };
  }

  refresh() {
    console.log("Refreshing Sales Order Weekly Metrics");
    
    // Re-inherit display settings from parent component
    if (this.parentComponent && this.parentComponent.displaySettings) {
      this.displaySettings = { ...this.parentComponent.displaySettings };
      console.log("Refreshed display settings from parent:", this.displaySettings);
    }
    
    this.init();
  }

  render() {
    if (!this.container) {
      console.error("No container available for Sales Order Metrics");
      return;
    }

    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Calculating weekly metrics...</p>
      </div>
    `;
  }

  renderContent() {
    if (this.salesOrders.length === 0) {
      this.renderEmptyState();
      return;
    }

    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
          
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Week
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Orders
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Order Total
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Weekly WIP
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  WIP Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  On Hand Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Missing Value
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total Project COGs
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total Cumulative COGs
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderPastDueRow()}
              ${this.renderWeeklyRows()}
            </tbody>
          </table>
        </div>
          
        <!-- Summary Row -->
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-t border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-9 gap-4 text-sm">
            <div class="text-gray-900 dark:text-white font-medium">
              Total (${this.weeks.length} weeks + Past Due)
            </div>
            <div class="text-center text-gray-900 dark:text-white">
              ${this.pastDueMetrics.ordersCount + this.weeklyMetrics.reduce((sum, w) => sum + w.ordersCount, 0)}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, w) => sum + w.orderTotal, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.weeklyWip + this.weeklyMetrics.reduce((sum, w) => sum + w.weeklyWip, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.wipValue + this.weeklyMetrics.reduce((sum, w) => sum + w.wipValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.onHandValue + this.weeklyMetrics.reduce((sum, w) => sum + w.onHandValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.missingValue + this.weeklyMetrics.reduce((sum, w) => sum + w.missingValue, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.pastDueMetrics.projectCogs + this.weeklyMetrics.reduce((sum, w) => sum + w.projectCogs, 0))}
            </div>
            <div class="text-right text-gray-800 dark:text-gray-200">
              ${this.formatCurrency(this.weeklyMetrics.length > 0 ? this.weeklyMetrics[this.weeklyMetrics.length - 1].cumulativeCogs : this.pastDueMetrics.projectCogs)}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderPastDueRow() {
    return `
      <tr class="bg-red-50 dark:bg-red-900 hover:bg-red-100 dark:hover:bg-red-800 border-b-2 border-red-200 dark:border-red-700">
        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
          <div>
            <div class="font-medium text-red-700 dark:text-red-300">Past Due</div>
            <div class="text-xs text-red-600 dark:text-red-400">Before July 4, 2025</div>
          </div>
        </td>
        <td class="px-4 py-3 text-center text-sm text-gray-900 dark:text-white">
          <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${this.pastDueMetrics.ordersCount > 0 ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'} rounded-full">
            ${this.pastDueMetrics.ordersCount}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.orderTotal)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.weeklyWip)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.wipValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.onHandValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.missingValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.projectCogs)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-red-600 dark:text-red-400">
            ${this.formatCurrency(this.pastDueMetrics.projectCogs)}
          </span>
        </td>
      </tr>
    `;
  }

  renderWeeklyRows() {
    return this.weeklyMetrics.map((weekMetric, index) => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
        <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
          <div>
            <div class="font-medium">Week ${weekMetric.week.weekNumber}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${weekMetric.week.label}</div>
          </div>
        </td>
        <td class="px-4 py-3 text-center text-sm text-gray-900 dark:text-white">
          <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${weekMetric.ordersCount > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'}">
            ${weekMetric.ordersCount}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(weekMetric.orderTotal)}
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.weeklyWip > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.weeklyWip)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.wipValue > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.wipValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.onHandValue > 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.onHandValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.missingValue > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">
            ${this.formatCurrency(weekMetric.missingValue)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="${weekMetric.projectCogs > 0 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
            ${this.formatCurrency(weekMetric.projectCogs)}
          </span>
        </td>
        <td class="px-4 py-3 text-sm text-right text-gray-800 dark:text-gray-200">
          <span class="text-blue-600 dark:text-blue-400">
            ${this.formatCurrency(weekMetric.cumulativeCogs)}
          </span>
        </td>
      </tr>
    `).join('');
  }

  renderEmptyState() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="text-gray-600 dark:text-gray-400">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <p class="text-center text-lg font-medium mb-2">No sales order data available</p>
          <p class="text-center text-sm">Load sales orders to view weekly metrics analysis</p>
        </div>
      </div>
    `;
  }

  formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return this.displaySettings.showCurrency ? '$0.00' : '0.00';
    }
    
    if (this.displaySettings.showCurrency) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'CAD',
        minimumFractionDigits: 2
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    }
  }

  formatDateShort(date) {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }

  showError(message) {
    console.error("Sales Order Weekly Metrics Error:", message);
    
    if (this.container) {
      this.container.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }



  // Export functionality for metrics data
  exportToCSV() {
    if (this.weeklyMetrics.length === 0 && this.pastDueMetrics.ordersCount === 0) {
      this.showError('No metrics data to export');
      return;
    }

    try {
      // Prepare CSV headers
      const headers = [
        'Week',
        'Orders Count',
        'Order Total',
        'Weekly WIP',
        'WIP Value',
        'On Hand Value',
        'Missing Value',
        'Total Project COGs',
        'Total Cumulative COGs'
      ];

      // Prepare CSV rows - start with Past Due
      const rows = [];
      
      // Add Past Due row
      rows.push([
        'Past Due (Before July 4, 2025)',
        this.pastDueMetrics.ordersCount,
        this.formatNumberForExport(this.pastDueMetrics.orderTotal),
        this.formatNumberForExport(this.pastDueMetrics.weeklyWip),
        this.formatNumberForExport(this.pastDueMetrics.wipValue),
        this.formatNumberForExport(this.pastDueMetrics.onHandValue),
        this.formatNumberForExport(this.pastDueMetrics.missingValue),
        this.formatNumberForExport(this.pastDueMetrics.projectCogs),
        this.formatNumberForExport(this.pastDueMetrics.projectCogs)
      ]);

      // Add weekly metrics rows
      this.weeklyMetrics.forEach(weekMetric => {
        rows.push([
          `Week ${weekMetric.week.weekNumber} (${weekMetric.week.label})`,
          weekMetric.ordersCount,
          this.formatNumberForExport(weekMetric.orderTotal),
          this.formatNumberForExport(weekMetric.weeklyWip),
          this.formatNumberForExport(weekMetric.wipValue),
          this.formatNumberForExport(weekMetric.onHandValue),
          this.formatNumberForExport(weekMetric.missingValue),
          this.formatNumberForExport(weekMetric.projectCogs),
          this.formatNumberForExport(weekMetric.cumulativeCogs)
        ]);
      });

      // Add totals row
      const totalOrders = this.pastDueMetrics.ordersCount + this.weeklyMetrics.reduce((sum, w) => sum + w.ordersCount, 0);
      const totalOrderTotal = this.pastDueMetrics.orderTotal + this.weeklyMetrics.reduce((sum, w) => sum + w.orderTotal, 0);
      const totalWeeklyWip = this.pastDueMetrics.weeklyWip + this.weeklyMetrics.reduce((sum, w) => sum + w.weeklyWip, 0);
      const totalWipValue = this.pastDueMetrics.wipValue + this.weeklyMetrics.reduce((sum, w) => sum + w.wipValue, 0);
      const totalOnHandValue = this.pastDueMetrics.onHandValue + this.weeklyMetrics.reduce((sum, w) => sum + w.onHandValue, 0);
      const totalMissingValue = this.pastDueMetrics.missingValue + this.weeklyMetrics.reduce((sum, w) => sum + w.missingValue, 0);
      const totalProjectCogs = this.pastDueMetrics.projectCogs + this.weeklyMetrics.reduce((sum, w) => sum + w.projectCogs, 0);
      const finalCumulativeCogs = this.weeklyMetrics.length > 0 ? this.weeklyMetrics[this.weeklyMetrics.length - 1].cumulativeCogs : this.pastDueMetrics.projectCogs;

      rows.push([
        `TOTAL (${this.weeklyMetrics.length} weeks + Past Due)`,
        totalOrders,
        this.formatNumberForExport(totalOrderTotal),
        this.formatNumberForExport(totalWeeklyWip),
        this.formatNumberForExport(totalWipValue),
        this.formatNumberForExport(totalOnHandValue),
        this.formatNumberForExport(totalMissingValue),
        this.formatNumberForExport(totalProjectCogs),
        this.formatNumberForExport(finalCumulativeCogs)
      ]);

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.map(field => {
          // Escape fields that contain commas, quotes, or line breaks
          const fieldStr = String(field || '');
          if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
            return `"${fieldStr.replace(/"/g, '""')}"`;
          }
          return fieldStr;
        }).join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_order_metrics_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Exported Sales Order Metrics to CSV: ${this.weeklyMetrics.length} weeks + Past Due`);
      
      // Show success message
      this.showSuccess(`Exported ${this.weeklyMetrics.length + 1} rows of metrics data to CSV`);
    } catch (error) {
      console.error('Error exporting metrics to CSV:', error);
      this.showError('Failed to export metrics data: ' + error.message);
    }
  }

  // Format numbers for export without currency symbols
  formatNumberForExport(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return '0.00';
    }
    
    return amount.toFixed(2);
  }

  showSuccess(message) {
    console.log("Sales Order Metrics Success:", message);
    
    // Create temporary success message
    const successElement = document.createElement('div');
    successElement.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
    successElement.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <span class="block sm:inline">${message}</span>
        </div>
        <div class="ml-auto pl-3">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-green-500 hover:text-green-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(successElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (successElement.parentElement) {
        successElement.parentElement.removeChild(successElement);
      }
    }, 5000);
  }
} 